# Typography.Text Component

一个完整实现的 Ant Design Vue Typography.Text 组件，支持所有官方功能特性。

## 功能特性

### 基础样式
- ✅ `code` - 代码样式
- ✅ `mark` - 标记样式  
- ✅ `delete` - 删除线样式
- ✅ `underline` - 下划线样式
- ✅ `strong` - 粗体样式
- ✅ `italic` - 斜体样式
- ✅ `keyboard` - 键盘样式

### 文本类型和状态
- ✅ `type` - 文本类型 (secondary, success, warning, danger)
- ✅ `disabled` - 禁用状态

### 交互功能
- ✅ `copyable` - 复制功能
  - 支持自定义复制文本
  - 支持自定义图标
  - 支持自定义提示文本
  - 支持复制回调
  - 支持不同格式 (text/plain, text/html)

- ✅ `editable` - 编辑功能
  - 支持编辑状态控制
  - 支持自定义图标
  - 支持最大长度限制
  - 支持自动调整大小
  - 支持多种触发方式 (icon, text)
  - 支持编辑回调

- ✅ `ellipsis` - 省略功能
  - 支持单行和多行省略
  - 支持展开/收起
  - 支持自定义省略符号
  - 支持自定义后缀
  - 支持提示工具

### 事件处理
- ✅ `onClick` - 点击事件
- ✅ `onCopy` - 复制事件
- ✅ `onEditStart` - 开始编辑事件
- ✅ `onEditEnd` - 结束编辑事件
- ✅ `onEditCancel` - 取消编辑事件
- ✅ `onEditChange` - 编辑变化事件
- ✅ `onExpand` - 展开/收起事件

### 样式和主题
- ✅ 完整的 CSS 样式实现
- ✅ 响应式设计支持
- ✅ 暗色主题支持
- ✅ 高对比度模式支持
- ✅ 减少动画模式支持

## 安装和使用

### 安装依赖
```bash
npm install vue@^3.0.0 @vue/compiler-sfc vite @vitejs/plugin-vue
```

### 基础使用
```vue
<template>
  <div>
    <!-- 基础文本 -->
    <TypographyText>默认文本</TypographyText>
    
    <!-- 代码样式 -->
    <TypographyText code>代码文本</TypographyText>
    
    <!-- 复制功能 -->
    <TypographyText copyable>可复制文本</TypographyText>
    
    <!-- 编辑功能 -->
    <TypographyText editable>可编辑文本</TypographyText>
    
    <!-- 省略功能 -->
    <TypographyText :ellipsis="{ expandable: true }">
      很长的文本内容...
    </TypographyText>
  </div>
</template>

<script>
import TypographyText from './components/Typography/Text.vue'

export default {
  components: {
    TypographyText
  }
}
</script>
```

### 高级配置

#### 复制功能配置
```vue
<TypographyText 
  :copyable="{
    text: '自定义复制文本',
    icon: ['📋', '✅'],
    tooltips: ['点击复制', '复制成功'],
    format: 'text/plain',
    onCopy: (event, { text }) => console.log('复制了:', text)
  }"
>
  显示文本
</TypographyText>
```

#### 编辑功能配置
```vue
<TypographyText 
  :editable="{
    icon: '🖊️',
    tooltip: '点击编辑',
    maxLength: 100,
    autoSize: true,
    triggerType: ['icon', 'text'],
    enterIcon: '✅',
    onChange: (text) => console.log('编辑中:', text),
    onStart: () => console.log('开始编辑'),
    onEnd: (text) => console.log('编辑完成:', text)
  }"
>
  可编辑文本
</TypographyText>
```

#### 省略功能配置
```vue
<TypographyText 
  :ellipsis="{
    rows: 3,
    expandable: true,
    suffix: '...',
    symbol: '展开',
    tooltip: '点击查看完整内容',
    onExpand: (event, { expanded }) => console.log('展开状态:', expanded)
  }"
>
  很长的多行文本内容...
</TypographyText>
```

## API 参考

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| code | boolean | false | 代码样式 |
| copyable | boolean \| object | false | 是否可复制 |
| delete | boolean | false | 删除线样式 |
| disabled | boolean | false | 禁用状态 |
| editable | boolean \| object | false | 是否可编辑 |
| ellipsis | boolean \| object | false | 自动省略 |
| keyboard | boolean | false | 键盘样式 |
| mark | boolean | false | 标记样式 |
| strong | boolean | false | 粗体样式 |
| italic | boolean | false | 斜体样式 |
| type | string | - | 文本类型 |
| underline | boolean | false | 下划线样式 |
| text | string | '' | 文本内容 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| click | (event) | 点击事件 |
| copy | ({ text }) | 复制事件 |
| edit-start | () | 开始编辑 |
| edit-end | (text) | 结束编辑 |
| edit-cancel | () | 取消编辑 |
| edit-change | (text) | 编辑变化 |
| expand | ({ expanded }) | 展开/收起 |

## 开发和测试

### 启动开发服务器
```bash
npm run dev
```

### 构建项目
```bash
npm run build
```

### 运行测试
```bash
npm run test
```

## 浏览器兼容性

- Chrome >= 60
- Firefox >= 55
- Safari >= 12
- Edge >= 79

## 许可证

MIT License
