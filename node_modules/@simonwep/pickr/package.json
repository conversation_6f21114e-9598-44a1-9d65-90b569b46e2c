{"name": "@simonwep/pickr", "version": "1.8.2", "license": "MIT", "author": "<PERSON> <<EMAIL>>", "description": "Flat, Simple, Hackable Color-Picker.", "keywords": ["ux", "color", "color-picker"], "main": "./dist/pickr.min.js", "typings": "./types/pickr.d.ts", "scripts": {"build": "node ./scripts/build.js", "dev": "webpack serve --mode development", "lint": "eslint ./src", "lint:fix": "eslint ./src --fix", "dryrun": "npm run lint:fix && npm run build"}, "devDependencies": {"@babel/core": "^7.14.6", "@babel/plugin-proposal-class-properties": "^7.14.5", "@babel/plugin-proposal-object-rest-spread": "^7.14.7", "@babel/plugin-transform-parameters": "^7.14.5", "@babel/preset-env": "^7.14.7", "autoprefixer": "^10.2.6", "babel-eslint": "^10.1.0", "babel-loader": "^8.2.2", "css-loader": "^5.2.6", "eslint": "^7.29.0", "eslint-config-simon": "^2.1.0", "eslint-loader": "^4.0.2", "mini-css-extract-plugin": "^1.6.1", "node-sass": "^6.0.1", "postcss-loader": "^6.1.0", "sass-loader": "^12.1.0", "terser-webpack-plugin": "^5.1.4", "webpack": "^5.40.0", "webpack-cleanup-plugin": "^0.5.1", "webpack-cli": "^4.7.2", "webpack-dev-server": "^4.0.0-beta.3", "webpack-fix-style-only-entries": "^0.6.1"}, "homepage": "https://github.com/Simonwep/pickr#readme", "repository": {"type": "git", "url": "https://github.com/Simonwep/pickr.git"}, "bugs": {"url": "https://github.com/Simonwep/pickr/issues"}, "dependencies": {"core-js": "^3.15.1", "nanopop": "^2.1.0"}}