@import 'variables';

// Pseudo style reset
@mixin pseudo-reset {
    position: absolute;
    content: '';
    top: 0;
    left: 0;
}

@mixin transparency-background($size: 0.5em) {
    &::before {
        @include pseudo-reset;
        width: 100%;
        height: 100%;
        background: $icon-transparency;
        background-size: $size;
        border-radius: $border-radius-mid;
        z-index: -1;
    }
}
