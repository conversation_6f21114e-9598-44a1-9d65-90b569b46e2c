@import '../lib/variables';
@import '../lib/mixins';
@import '../base';

.pcr-app[data-theme='classic'] {
    width: 28.5em;
    max-width: 95vw;
    padding: 0.8em;

    .pcr-selection {
        display: flex;
        justify-content: space-between;
        flex-grow: 1;

        .pcr-color-preview {
            @include transparency-background;
            position: relative;
            z-index: 1;
            width: 2em;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            margin-right: 0.75em;

            .pcr-last-color {
                cursor: pointer;
                border-radius: 0.15em 0.15em 0 0;
                z-index: 2;
            }

            .pcr-current-color {
                border-radius: 0 0 0.15em 0.15em;
            }

            .pcr-last-color,
            .pcr-current-color {
                background: var(--pcr-color);
                width: 100%;
                height: 50%;
            }
        }

        .pcr-color-palette {
            width: 100%;
            height: 8em;
            z-index: 1;

            .pcr-palette {
                flex-grow: 1;
                border-radius: $border-radius-mid;
                @include transparency-background;
            }
        }

        .pcr-color-chooser,
        .pcr-color-opacity {
            margin-left: 0.75em;

            .pcr-picker {
                left: 50%;
                transform: translateX(-50%);
            }

            .pcr-slider {
                width: 8px;
                flex-grow: 1;
                border-radius: 50em;
            }
        }

        .pcr-color-chooser .pcr-slider {
            background: colorRainbow();
        }

        .pcr-color-opacity .pcr-slider {
            background: linear-gradient(to bottom, transparent, black), $icon-transparency;
            background-size: 100%, 50%;
        }
    }
}
