{"version": 3, "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///webpack/bootstrap", "webpack:///webpack/runtime/define property getters", "webpack:///webpack/runtime/hasOwnProperty shorthand", "webpack:///webpack/runtime/make namespace object", "webpack:///./src/js/utils/utils.js", "webpack:///./src/js/utils/color.js", "webpack:///./src/js/utils/hsvacolor.js", "webpack:///./src/js/libs/moveable.js", "webpack:///./src/js/libs/selectable.js", "webpack:///./src/js/template.js", "webpack:///../src/NanoPop.ts", "webpack:///./src/js/pickr.js"], "names": ["root", "factory", "exports", "module", "define", "amd", "self", "__webpack_require__", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "Symbol", "toStringTag", "value", "eventListener", "method", "elements", "events", "fn", "options", "HTMLCollection", "NodeList", "Array", "from", "isArray", "el", "ev", "capture", "slice", "arguments", "on", "bind", "off", "createElementFromString", "html", "div", "document", "createElement", "innerHTML", "trim", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "createFromTemplate", "str", "removeAttribute", "name", "getAttribute", "resolve", "element", "base", "con", "subtree", "child", "children", "arr", "sub", "push", "keys", "length", "eventPath", "evt", "path", "<PERSON><PERSON><PERSON>", "target", "parentElement", "window", "resolveElement", "val", "Element", "split", "reduce", "pv", "cv", "ci", "a", "querySelector", "shadowRoot", "adjustableInputNumbers", "mapper", "v", "handleScroll", "e", "inc", "Number", "shift<PERSON>ey", "ctrl<PERSON>ey", "deltaY", "index", "selectionStart", "replace", "i", "focus", "setSelectionRange", "preventDefault", "dispatchEvent", "Event", "passive", "min", "max", "floor", "round", "Math", "hsvToRgb", "h", "s", "f", "p", "q", "t", "mod", "hsvToHsl", "l", "rgbToHsv", "r", "g", "b", "minVal", "maxVal", "delta", "dr", "dg", "db", "cmykToHsv", "c", "m", "y", "k", "hslToHsv", "ns", "isNaN", "hexToHsv", "hex", "match", "map", "parseInt", "parseToHSVA", "toLowerCase", "ctx", "getContext", "fillStyle", "standardizeColor", "regex", "cmyk", "rgba", "hsla", "hsva", "hexa", "numarize", "array", "test", "undefined", "invalid", "type", "exec", "alphaValid", "values", "join", "raw", "substring", "HSVaColor", "original", "next", "precision", "toFixed", "that", "toHSVA", "toString", "toHSLA", "toRGBA", "toCMYK", "rgb", "hsvToCmyk", "toHEXA", "padStart", "hsvToHex", "alpha", "toUpperCase", "clone", "clamp", "Moveable", "opt", "assign", "lock", "onchange", "onstop", "_keyboard", "activeElement", "wrapper", "up", "right", "down", "left", "xm", "ym", "update", "cache", "x", "startsWith", "_tapstart", "_", "_tapstop", "_tapmove", "cancelable", "getBoundingClientRect", "touch", "touches", "clientX", "clientY", "width", "top", "height", "style", "offsetWidth", "offsetHeight", "cx", "cy", "trigger", "destroy", "Selectable", "className", "onTap", "for<PERSON>ach", "classList", "stopPropagation", "defaults", "variantFlipOrder", "start", "middle", "end", "positionFlipOrder", "bottom", "position", "margin", "reposition", "reference", "popper", "container", "documentElement", "originalLeft", "originalTop", "refBox", "popBox", "positionStore", "variantStore", "vs", "vm", "ve", "hs", "hm", "he", "pos<PERSON>ey", "<PERSON><PERSON><PERSON>", "positions", "variants", "vertical", "positionVal", "<PERSON><PERSON><PERSON>", "variant<PERSON><PERSON>", "positionSize", "variantSize", "positionMaximum", "variantMaximum", "positionMinimum", "variantMinimum", "variantVal", "Pickr", "constructor", "init", "save", "hide", "show", "clear", "change", "changestop", "cancel", "swatchselect", "this", "DEFAULT_OPTIONS", "swatches", "components", "theme", "sliders", "lockOpacity", "padding", "includes", "interaction", "preview", "opacity", "hue", "palette", "_preBuild", "_buildComponents", "_bindEvents", "_finalBuild", "color", "addSwatch", "button", "app", "_root", "_nanopop", "baseOptions", "HTMLElement", "[object Object]", "Error", "createPopper", "setAttribute", "_t", "_setupAnimationFrame", "requestAnimationFrame", "cb", "setColor", "default", "_rePositioningPicker", "defaultRepresentation", "_representation", "setColorRepresentation", "showAlways", "_initializingActive", "_emit", "instance", "useAsButton", "inline", "appClass", "hidden", "input", "int", "find", "add", "contains", "buildPickr", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "parent", "nextS<PERSON>ling", "insertBefore", "remove", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "disabled", "disable", "comparison", "transition", "lastColor", "inst", "cs", "repeat", "so", "sh", "getColor", "_color", "_lastColor", "picker", "currentColor", "_recalc", "_updateOutput", "cssRGBaString", "background", "setProperty", "_swatchColors", "slider", "backgroundColor", "selectable", "_components", "eventBindings", "_clearColor", "setHSVA", "applyColor", "result", "stopImmediatePropagation", "ck", "closeWithKey", "isOpen", "code", "some", "adjustableNumbers", "ranges", "step", "range", "getColorRepresentation", "nv", "toPrecision", "autoReposition", "timeout", "closeOnScroll", "setTimeout", "rs", "clearTimeout", "_eventBindings", "body", "eb", "innerHeight", "innerWidth", "eventSource", "outputPrecision", "silent", "_parseLocalColor", "alphaMakesAChange", "i18n", "I18N_DEFAULTS", "event", "args", "_eventListener", "callBacks", "indexOf", "splice", "removeSwatch", "swatchColor", "cancelAnimationFrame", "destroyAndRemove", "recalc", "string", "utype", "click", "getSelectedColor", "getRoot", "enable", "VERSION"], "mappings": ";CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAe,MAAID,IAEnBD,EAAY,MAAIC,IARlB,CASGK,MAAM,WACT,M,mBCTA,IAAIC,EAAsB,CCA1B,EAAwB,CAACL,EAASM,KACjC,IAAI,IAAIC,KAAOD,EACXD,EAAoBG,EAAEF,EAAYC,KAASF,EAAoBG,EAAER,EAASO,IAC5EE,OAAOC,eAAeV,EAASO,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,MCJ3E,EAAwB,CAACM,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,GCClF,EAAyBd,IACH,oBAAXkB,QAA0BA,OAAOC,aAC1CV,OAAOC,eAAeV,EAASkB,OAAOC,YAAa,CAAEC,MAAO,WAE7DX,OAAOC,eAAeV,EAAS,aAAc,CAAEoB,OAAO,M,qCCJvD,SAASC,EAAcC,EAAQC,EAAUC,EAAQC,EAAIC,EAAU,IAGvDH,aAAoBI,gBAAkBJ,aAAoBK,SAC1DL,EAAWM,MAAMC,KAAKP,GACdM,MAAME,QAAQR,KACtBA,EAAW,CAACA,IAGXM,MAAME,QAAQP,KACfA,EAAS,CAACA,IAGd,IAAK,MAAMQ,KAAMT,EACb,IAAK,MAAMU,KAAMT,EACbQ,EAAGV,GAAQW,EAAIR,EAAI,CAACS,SAAS,KAAUR,IAI/C,OAAOG,MAAMd,UAAUoB,MAAMlB,KAAKmB,UAAW,G,4JAW1C,MAAMC,EAAKhB,EAAciB,KAAK,KAAM,oBAU9BC,EAAMlB,EAAciB,KAAK,KAAM,uBAOrC,SAASE,EAAwBC,GACpC,MAAMC,EAAMC,SAASC,cAAc,OAEnC,OADAF,EAAIG,UAAYJ,EAAKK,OACdJ,EAAIK,kBAkBR,SAASC,EAAmBC,GAG/B,MAAMC,EAAkB,CAAClB,EAAImB,KACzB,MAAM/B,EAAQY,EAAGoB,aAAaD,GAE9B,OADAnB,EAAGkB,gBAAgBC,GACZ/B,GAILiC,EAAU,CAACC,EAASC,EAAO,MAG7B,MAAMC,EAAMN,EAAgBI,EAAS,QAC/B/C,EAAM2C,EAAgBI,EAAS,QAC/BG,EAAUD,EAAOD,EAAKC,GAAO,GAAMD,EAGzChD,IAAQgD,EAAKhD,GAAO+C,GACpB,IAAK,MAAMI,KAAS7B,MAAMC,KAAKwB,EAAQK,UAAW,CAC9C,MAAMC,EAAMV,EAAgBQ,EAAO,QAC7BG,EAAMR,EAAQK,EAAOE,EAAM,GAAKH,GAElCG,IAGCH,EAAQG,KAASH,EAAQG,GAAO,KAC5BE,KAAKrD,OAAOsD,KAAKF,GAAKG,OAASH,EAAMH,GAIlD,OAAOH,GAGX,OAAOF,EAAQb,EAAwBS,IAQpC,SAASgB,EAAUC,GACtB,IAAIC,EAAOD,EAAIC,MAASD,EAAIE,cAAgBF,EAAIE,eAChD,GAAID,EACA,OAAOA,EAGX,IAAInC,EAAKkC,EAAIG,OAAOC,cAEpB,IADAH,EAAO,CAACD,EAAIG,OAAQrC,GACbA,EAAKA,EAAGsC,eACXH,EAAKL,KAAK9B,GAId,OADAmC,EAAKL,KAAKnB,SAAU4B,QACbJ,EAQJ,SAASK,EAAeC,GAC3B,OAAIA,aAAeC,QACRD,EACe,iBAARA,EACPA,EAAIE,MAAM,OAAOC,QAAO,CAACC,EAAIC,EAAIC,EAAIC,KACxCH,EAAKA,EAAGI,cAAcH,GACfC,EAAKC,EAAEhB,OAAS,EAAIa,EAAGK,WAAaL,IAC5ClC,UAGA,KAQJ,SAASwC,EAAuBnD,EAAIoD,EAASC,IAAKA,IAErD,SAASC,EAAaC,GAClB,MAAMC,EAAO,CAAC,KAAO,IAAM,IAAMC,OAAOF,EAAEG,UAAwB,EAAZH,EAAEI,WAAiBJ,EAAEK,OAAS,EAAI,GAAK,GAE7F,IAAIC,EAAQ,EACRtD,EAAMP,EAAG8D,eACb9D,EAAGZ,MAAQY,EAAGZ,MAAM2E,QAAQ,WAAW,CAACV,EAAGW,IAGnCA,GAAKzD,GAAOyD,EAAIX,EAAErB,QAAUzB,GAC5BA,EAAMyD,EACCZ,EAAOK,OAAOJ,GAAIG,EAAKK,KAGlCA,IACOR,KAGXrD,EAAGiE,QACHjE,EAAGkE,kBAAkB3D,EAAKA,GAG1BgD,EAAEY,iBACFnE,EAAGoE,cAAc,IAAIC,MAAM,UAI/BhE,EAAGL,EAAI,SAAS,IAAMK,EAAGkC,OAAQ,QAASe,EAAc,CAACgB,SAAS,MAClEjE,EAAGL,EAAI,QAAQ,IAAMO,EAAIgC,OAAQ,QAASe,KClL9C,MAAM,IAACiB,EAAD,IAAMC,EAAN,MAAWC,EAAX,MAAkBC,GAASC,KA0B1B,SAASC,EAASC,EAAGC,EAAGzB,GAE3ByB,GAAK,IACLzB,GAAK,IAEL,MAAMW,EAAIS,EAJVI,EAAKA,EAAI,IAAO,GAMVE,EAAIF,EAAIb,EACRgB,EAAI3B,GAAK,EAAIyB,GACbG,EAAI5B,GAAK,EAAI0B,EAAID,GACjBI,EAAI7B,GAAK,GAAK,EAAI0B,GAAKD,GAEvBK,EAAMnB,EAAI,EAKhB,MAAO,CACC,IALE,CAACX,EAAG4B,EAAGD,EAAGA,EAAGE,EAAG7B,GAAG8B,GAMrB,IALE,CAACD,EAAG7B,EAAGA,EAAG4B,EAAGD,EAAGA,GAAGG,GAMrB,IALE,CAACH,EAAGA,EAAGE,EAAG7B,EAAGA,EAAG4B,GAAGE,IAuD1B,SAASC,EAASP,EAAGC,EAAGzB,GAI3B,MAAMgC,GAAK,GAHXP,GAAK,OACLzB,GAAK,KAEmB,EAYxB,OAVU,IAANgC,IAEIP,EADM,IAANO,EACI,EACGA,EAAI,GACPP,EAAIzB,GAAS,EAAJgC,GAETP,EAAIzB,GAAK,EAAQ,EAAJgC,IAIlB,CACHR,EACI,IAAJC,EACI,IAAJO,GAWR,SAASC,EAASC,EAAGC,EAAGC,GAKpB,MAAMC,EAASnB,EAJfgB,GAAK,IACLC,GAAK,IACLC,GAAK,KAGCE,EAASnB,EAAIe,EAAGC,EAAGC,GACnBG,EAAQD,EAASD,EAEvB,IAAIb,EAAGC,EAEP,GAAc,IAAVc,EACAf,EAAIC,EAAI,MACL,CACHA,EAAIc,EAAQD,EACZ,MAAME,IAAQF,EAASJ,GAAK,EAAMK,EAAQ,GAAMA,EAC1CE,IAAQH,EAASH,GAAK,EAAMI,EAAQ,GAAMA,EAC1CG,IAAQJ,EAASF,GAAK,EAAMG,EAAQ,GAAMA,EAE5CL,IAAMI,EACNd,EAAIkB,EAAKD,EACFN,IAAMG,EACbd,EAAK,EAAI,EAAKgB,EAAKE,EACZN,IAAME,IACbd,EAAK,EAAI,EAAKiB,EAAKD,GAGnBhB,EAAI,EACJA,GAAK,EACEA,EAAI,IACXA,GAAK,GAIb,MAAO,CACC,IAAJA,EACI,IAAJC,EACI,IA3BEa,GAuCd,SAASK,EAAUC,EAAGC,EAAGC,EAAGC,GAExBF,GAAK,IACLC,GAAK,IAOL,MAAO,IAAIb,EAJ+B,KAA/B,EAAIf,EAAI,GALnB0B,GAAK,MAKsB,GAF3BG,GAAK,MAE+BA,IACM,KAA/B,EAAI7B,EAAI,EAAG2B,GAAK,EAAIE,GAAKA,IACM,KAA/B,EAAI7B,EAAI,EAAG4B,GAAK,EAAIC,GAAKA,MAYxC,SAASC,EAASxB,EAAGC,EAAGO,GACpBP,GAAK,IAIL,MAAMwB,EAAM,GAFZxB,IADAO,GAAK,KACI,GAAMA,EAAI,EAAIA,IAEFA,EAAIP,GAAM,IACzBzB,EAAc,KAATgC,EAAIP,GACf,MAAO,CAACD,EAAG0B,MAAMD,GAAM,EAAIA,EAAIjD,GAQnC,SAASmD,EAASC,GACd,OAAOnB,KAAYmB,EAAIC,MAAM,SAASC,KAAItD,GAAKuD,SAASvD,EAAG,OASxD,SAASwD,EAAY5F,GAGxBA,EAAMA,EAAIyF,MAAM,eAxNpB,SAA0BvF,GAGtB,GAA2B,UAAvBA,EAAK2F,cACL,MAAO,OAGX,MAAMC,EAAMpG,SAASC,cAAc,UAAUoG,WAAW,MAExD,OADAD,EAAIE,UAAY9F,EACS,SAAlB4F,EAAIE,UAAuB,KAAOF,EAAIE,UA+MZC,CAAiBjG,GAAOA,EAGzD,MAAMkG,EAAQ,CACVC,KAAM,6DACNC,KAAM,wEACNC,KAAM,wEACNC,KAAM,wEACNC,KAAM,4DASJC,EAAWC,GAASA,EAAMf,KAAItD,GAAK,oBAAoBsE,KAAKtE,GAAKI,OAAOJ,QAAKuE,IAEnF,IAAIlB,EACJmB,EAAS,IAAK,MAAMC,KAAQX,EAAO,CAG/B,KAAMT,EAAQS,EAAMW,GAAMC,KAAK9G,IAC3B,SAIJ,MAAM+G,EAAahF,KAAQ0D,EAAM,KAAqB,iBAAN1D,GAGhD,OAAQ8E,GACJ,IAAK,OAAQ,CACT,MAAO,CAAE7B,EAAGC,EAAGC,EAAGC,GAAKqB,EAASf,GAEhC,GAAIT,EAAI,KAAOC,EAAI,KAAOC,EAAI,KAAOC,EAAI,IACrC,MAAMyB,EAGV,MAAO,CAACI,OAAQjC,EAAUC,EAAGC,EAAGC,EAAGC,GAAI0B,QAE3C,IAAK,OAAQ,CACT,MAAO,CAAC,CAAF,CAAOvC,EAAGC,EAAGC,EAAGzC,GAAKyE,EAASf,GAEpC,GAAInB,EAAI,KAAOC,EAAI,KAAOC,EAAI,KAAOzC,EAAI,GAAKA,EAAI,IAAMgF,EAAWhF,GAC/D,MAAM6E,EAGV,MAAO,CAACI,OAAQ,IAAI3C,EAASC,EAAGC,EAAGC,GAAIzC,GAAIA,EAAG8E,QAElD,IAAK,OAAQ,CACT,IAAK,CAAErB,GAAOC,EAEK,IAAfD,EAAIzE,QAA+B,IAAfyE,EAAIzE,SACxByE,EAAMA,EAAI9D,MAAM,IAAIgE,KAAItD,GAAKA,EAAIA,IAAG6E,KAAK,KAG7C,MAAMC,EAAM1B,EAAI2B,UAAU,EAAG,GAC7B,IAAIpF,EAAIyD,EAAI2B,UAAU,GAKtB,OAFApF,EAAIA,EAAK4D,SAAS5D,EAAG,IAAM,SAAO4E,EAE3B,CAACK,OAAQ,IAAIzB,EAAS2B,GAAMnF,GAAIA,IAAG8E,QAE9C,IAAK,OAAQ,CACT,MAAO,CAAC,CAAF,CAAOjD,EAAGC,EAAGO,EAAGrC,GAAKyE,EAASf,GAEpC,GAAI7B,EAAI,KAAOC,EAAI,KAAOO,EAAI,KAAOrC,EAAI,GAAKA,EAAI,IAAMgF,EAAWhF,GAC/D,MAAM6E,EAGV,MAAO,CAACI,OAAQ,IAAI5B,EAASxB,EAAGC,EAAGO,GAAIrC,GAAIA,EAAG8E,QAElD,IAAK,OAAQ,CACT,MAAO,CAAC,CAAF,CAAOjD,EAAGC,EAAGzB,EAAGL,GAAKyE,EAASf,GAEpC,GAAI7B,EAAI,KAAOC,EAAI,KAAOzB,EAAI,KAAOL,EAAI,GAAKA,EAAI,IAAMgF,EAAWhF,GAC/D,MAAM6E,EAGV,MAAO,CAACI,OAAQ,CAACpD,EAAGC,EAAGzB,EAAGL,GAAIA,EAAG8E,UAK7C,MAAO,CAACG,OAAQ,KAAMH,KAAM,MChTzB,SAASO,EAAUxD,EAAI,EAAGC,EAAI,EAAGzB,EAAI,EAAGL,EAAI,GAC/C,MAAMI,EAAS,CAACkF,EAAUC,IAAS,CAACC,GAAY,IACrCD,GAAMC,EAAYF,EAAS3B,KAAItD,GAAKI,OAAOJ,EAAEoF,QAAQD,MAAeF,GAGzEI,EAAO,CACT7D,IAAGC,IAAGzB,IAAGL,IAET2F,SACI,MAAMpB,EAAO,CAACmB,EAAK7D,EAAG6D,EAAK5D,EAAG4D,EAAKrF,EAAGqF,EAAK1F,GAE3C,OADAuE,EAAKqB,SAAWxF,EAAOmE,GAAM3F,GAAQ,QAAOA,EAAI,OAAOA,EAAI,QAAQA,EAAI,QAAQ8G,EAAK1F,OAC7EuE,GAGXsB,SACI,MAAMvB,EAAO,IAAIlC,EAASsD,EAAK7D,EAAG6D,EAAK5D,EAAG4D,EAAKrF,GAAIqF,EAAK1F,GAExD,OADAsE,EAAKsB,SAAWxF,EAAOkE,GAAM1F,GAAQ,QAAOA,EAAI,OAAOA,EAAI,QAAQA,EAAI,QAAQ8G,EAAK1F,OAC7EsE,GAGXwB,SACI,MAAMzB,EAAO,IAAIzC,EAAS8D,EAAK7D,EAAG6D,EAAK5D,EAAG4D,EAAKrF,GAAIqF,EAAK1F,GAExD,OADAqE,EAAKuB,SAAWxF,EAAOiE,GAAMzF,GAAQ,QAAOA,EAAI,OAAOA,EAAI,OAAOA,EAAI,OAAO8G,EAAK1F,OAC3EqE,GAGX0B,SACI,MAAM3B,EDsCX,SAAmBvC,EAAGC,EAAGzB,GAC5B,MAAM2F,EAAMpE,EAASC,EAAGC,EAAGzB,GACrBkC,EAAIyD,EAAI,GAAK,IACbxD,EAAIwD,EAAI,GAAK,IACbvD,EAAIuD,EAAI,GAAK,IAEb5C,EAAI7B,EAAI,EAAIgB,EAAG,EAAIC,EAAG,EAAIC,GAKhC,MAAO,CACC,KALQ,IAANW,EAAU,GAAK,EAAIb,EAAIa,IAAM,EAAIA,IAMnC,KALQ,IAANA,EAAU,GAAK,EAAIZ,EAAIY,IAAM,EAAIA,IAMnC,KALQ,IAANA,EAAU,GAAK,EAAIX,EAAIW,IAAM,EAAIA,IAMnC,IAAJA,GCrDiB6C,CAAUP,EAAK7D,EAAG6D,EAAK5D,EAAG4D,EAAKrF,GAE5C,OADA+D,EAAKwB,SAAWxF,EAAOgE,GAAMxF,GAAQ,QAAOA,EAAI,QAAQA,EAAI,QAAQA,EAAI,QAAQA,EAAI,SAC7EwF,GAGX8B,SACI,MAAMzC,EDmBX,SAAkB5B,EAAGC,EAAGzB,GAC3B,OAAOuB,EAASC,EAAGC,EAAGzB,GAAGsD,KAAItD,GACzBqB,EAAMrB,GAAGuF,SAAS,IAAIO,SAAS,EAAG,OCrBlBC,CAASV,EAAK7D,EAAG6D,EAAK5D,EAAG4D,EAAKrF,GAIpCgG,EAAQX,EAAK1F,GAAK,EAAI,GAAKS,QAAiB,IAATiF,EAAK1F,GAASyF,QAAQ,IAC1DG,SAAS,IACTU,cAAcH,SAAS,EAAG,KAI/B,OAFAE,GAAS5C,EAAI3E,KAAKuH,GAClB5C,EAAImC,SAAW,IAAO,IAAGnC,EAAIyB,KAAK,IAAIoB,gBAC/B7C,GAGX8C,MAAO,IAAMlB,EAAUK,EAAK7D,EAAG6D,EAAK5D,EAAG4D,EAAKrF,EAAGqF,EAAK1F,IAGxD,OAAO0F,ECrDX,MAAMc,EAAQnG,GAAKsB,KAAKH,IAAIG,KAAKJ,IAAIlB,EAAG,GAAI,GAC7B,SAASoG,EAASC,GAE7B,MAAMhB,EAAO,CAGThJ,QAASjB,OAAOkL,OAAO,CACnBC,KAAM,KACNC,SAAU,IAAM,EAChBC,OAAQ,IAAM,GACfJ,GAEHK,UAAUxG,GACN,MAAM,QAAC7D,GAAWgJ,GACZ,KAACZ,EAAD,IAAOvJ,GAAOgF,EAIpB,GAAI5C,SAASqJ,gBAAkBtK,EAAQuK,QAAS,CAC5C,MAAM,KAACL,GAAQlB,EAAKhJ,QACdwK,EAAa,YAAR3L,EACL4L,EAAgB,eAAR5L,EACR6L,EAAe,cAAR7L,EACP8L,EAAe,cAAR9L,EAEb,GAAa,YAATuJ,IAAuBoC,GAAMC,GAASC,GAAQC,GAAO,CACrD,IAAIC,EAAK,EACLC,EAAK,EAEI,MAATX,EACAU,EAAMJ,GAAMC,EAAS,GAAK,EACV,MAATP,EACPU,EAAMJ,GAAMC,GAAU,EAAI,GAE1BI,EAAKL,GAAM,EAAKE,EAAO,EAAI,EAC3BE,EAAKD,GAAQ,EAAKF,EAAQ,EAAI,GAGlCzB,EAAK8B,OACDhB,EAAMd,EAAK+B,MAAMC,EAAK,IAAOJ,GAC7Bd,EAAMd,EAAK+B,MAAMtE,EAAK,IAAOoE,IAEjChH,EAAEY,sBACK5F,EAAIoM,WAAW,WACtBjC,EAAKhJ,QAAQoK,SACbvG,EAAEY,oBAKdyG,UAAU1I,GACN2I,EAAKlK,SAAU,CAAC,UAAW,WAAY,eAAgB+H,EAAKoC,UAC5DD,EAAKlK,SAAU,CAAC,YAAa,aAAc+H,EAAKqC,UAE5C7I,EAAI8I,YACJ9I,EAAIiC,iBAIRuE,EAAKqC,SAAS7I,IAGlB6I,SAAS7I,GACL,MAAM,QAACxC,EAAD,MAAU+K,GAAS/B,GACnB,KAACkB,EAAD,QAAOtI,EAAP,QAAgB2I,GAAWvK,EAC3B+F,EAAIwE,EAAQgB,wBAElB,IAAIP,EAAI,EAAGvE,EAAI,EACf,GAAIjE,EAAK,CACL,MAAMgJ,EAAQhJ,GAAOA,EAAIiJ,SAAWjJ,EAAIiJ,QAAQ,GAChDT,EAAIxI,GAAOgJ,GAAShJ,GAAKkJ,QAAU,EACnCjF,EAAIjE,GAAOgJ,GAAShJ,GAAKmJ,QAAU,EAG/BX,EAAIjF,EAAE4E,KACNK,EAAIjF,EAAE4E,KACCK,EAAIjF,EAAE4E,KAAO5E,EAAE6F,QACtBZ,EAAIjF,EAAE4E,KAAO5E,EAAE6F,OAEfnF,EAAIV,EAAE8F,IACNpF,EAAIV,EAAE8F,IACCpF,EAAIV,EAAE8F,IAAM9F,EAAE+F,SACrBrF,EAAIV,EAAE8F,IAAM9F,EAAE+F,QAIlBd,GAAKjF,EAAE4E,KACPlE,GAAKV,EAAE8F,SACAd,IACPC,EAAID,EAAMC,EAAIjF,EAAE6F,MAChBnF,EAAIsE,EAAMtE,EAAIV,EAAE+F,QAGP,MAAT5B,IACAtI,EAAQmK,MAAMpB,KAAQ,QAAOK,EAAIjF,EAAE6F,MAAQ,UAAUhK,EAAQoK,YAAc,QAGlE,MAAT9B,IACAtI,EAAQmK,MAAMF,IAAO,QAAOpF,EAAIV,EAAE+F,OAAS,UAAUlK,EAAQqK,aAAe,QAGhFjD,EAAK+B,MAAQ,CAACC,EAAGA,EAAIjF,EAAE6F,MAAOnF,EAAGA,EAAIV,EAAE+F,QACvC,MAAMI,EAAKpC,EAAMkB,EAAIjF,EAAE6F,OACjBO,EAAKrC,EAAMrD,EAAIV,EAAE+F,QAEvB,OAAQ5B,GACJ,IAAK,IACD,OAAOlK,EAAQmK,SAAS+B,GAC5B,IAAK,IACD,OAAOlM,EAAQmK,SAASgC,GAC5B,QACI,OAAOnM,EAAQmK,SAAS+B,EAAIC,KAIxCf,WACIpC,EAAKhJ,QAAQoK,SACbe,EAAMlK,SAAU,CAAC,UAAW,WAAY,eAAgB+H,EAAKoC,UAC7DD,EAAMlK,SAAU,CAAC,YAAa,aAAc+H,EAAKqC,WAGrDe,UACIpD,EAAKqC,YAGTP,OAAOE,EAAI,EAAGvE,EAAI,GACd,MAAM,KAACkE,EAAD,IAAOkB,EAAP,MAAYD,EAAZ,OAAmBE,GAAU9C,EAAKhJ,QAAQuK,QAAQgB,wBAE9B,MAAtBvC,EAAKhJ,QAAQkK,OACbzD,EAAIuE,GAGRhC,EAAKqC,SAAS,CACVK,QAASf,EAAOiB,EAAQZ,EACxBW,QAASE,EAAMC,EAASrF,KAIhC4F,UACI,MAAM,QAACrM,EAAD,UAAUkL,EAAV,UAAqBb,GAAarB,EACxCmC,EAAMlK,SAAU,CAAC,UAAW,SAAUoJ,GACtCc,EAAM,CAACnL,EAAQuK,QAASvK,EAAQ4B,SAAU,YAAasJ,GACvDC,EAAM,CAACnL,EAAQuK,QAASvK,EAAQ4B,SAAU,aAAcsJ,EAAW,CAC/DtG,SAAS,OAMf,QAAC5E,EAAD,UAAUkL,EAAV,UAAqBb,GAAarB,EAQxC,OAPAmC,EAAK,CAACnL,EAAQuK,QAASvK,EAAQ4B,SAAU,YAAasJ,GACtDC,EAAK,CAACnL,EAAQuK,QAASvK,EAAQ4B,SAAU,aAAcsJ,EAAW,CAC9DtG,SAAS,IAGbuG,EAAKlK,SAAU,CAAC,UAAW,SAAUoJ,GAE9BrB,EC7JI,SAASsD,EAAWtC,EAAM,IACrCA,EAAMjL,OAAOkL,OAAO,CAChBE,SAAU,IAAM,EAChBoC,UAAW,GACX1M,SAAU,IACXmK,GAEH,MAAMwC,EAAQrB,EAAKnB,EAAInK,SAAU,SAAS2C,IACtCwH,EAAInK,SAAS4M,SAAQ5I,GACjBA,EAAE6I,UAAUlK,EAAIG,SAAWkB,EAAI,MAAQ,UAAUmG,EAAIuC,aAGzDvC,EAAIG,SAAS3H,GAGbA,EAAImK,qBAGR,MAAO,CACHN,QAAS,IAAMlB,KAASqB,ICnBhC,MCsDaI,EAAW,CACpBC,iBAAkB,CAAEC,MAAO,MAAOC,OAAQ,MAAOC,IAAK,OACtDC,kBAAmB,CAAEpB,IAAK,OAAQpB,MAAO,OAAQyC,OAAQ,OAAQvC,KAAM,QACvEwC,SAAU,SACVC,OAAQ,GAQCC,EAAa,CAACC,EAAwBC,EAAqBvD,KACpE,MAAMwD,UAAEA,EAASJ,OAAEA,EAAMD,SAAEA,EAAQN,iBAAEA,EAAgBI,kBAAEA,GAAsB,CACzEO,UAAWvM,SAASwM,gBAAgBlC,2BACjCqB,KACA5C,IAMCW,KAAM+C,EAAc7B,IAAK8B,GAAgBJ,EAAOxB,MACxDwB,EAAOxB,MAAMpB,KAAO,IACpB4C,EAAOxB,MAAMF,IAAM,IACnB,MAAM+B,EAASN,EAAU/B,wBACnBsC,EAASN,EAAOhC,wBAIhBuC,EAAoC,CACtCtI,EAAGoI,EAAO/B,IAAMgC,EAAO/B,OAASsB,EAChCrH,EAAG6H,EAAOV,OAASE,EACnBvH,EAAG+H,EAAOnD,MAAQ2C,EAClBzH,EAAGiI,EAAOjD,KAAOkD,EAAOjC,MAAQwB,GAM9BW,EAAkC,CACpCC,GAAIJ,EAAOjD,KACXsD,GAAIL,EAAOjD,KAAOiD,EAAOhC,MAAQ,GAAKiC,EAAOjC,MAAQ,EACrDsC,GAAIN,EAAOjD,KAAOiD,EAAOhC,MAAQiC,EAAOjC,MACxCuC,GAAIP,EAAO/B,IACXuC,GAAIR,EAAOV,OAASU,EAAO9B,OAAS,EAAI+B,EAAO/B,OAAS,EACxDuC,GAAIT,EAAOV,OAASW,EAAO/B,SAIxBwC,EAAQC,EAAS,UAAYpB,EAASlK,MAAM,KAC7CuL,EAAYvB,EAAkBqB,GAC9BG,EAAW5B,EAAiB0B,IAE5B1C,IAAEA,EAAGlB,KAAEA,EAAIuC,OAAEA,EAAMzC,MAAEA,GAAU+C,EACrC,IAAK,MAAMlI,KAAKkJ,EAAW,CACvB,MAAME,EAAkB,MAANpJ,GAAmB,MAANA,EAEzBqJ,EAAcb,EAAcxI,IAE3BsJ,EAAaC,GAAeH,EAAW,CAAC,MAAO,QAAU,CAAC,OAAQ,QAKlEI,EAAcC,GAAeL,EAAW,CAACb,EAAO/B,OAAQ+B,EAAOjC,OAAS,CAACiC,EAAOjC,MAAOiC,EAAO/B,SAC9FkD,EAAiBC,GAAkBP,EAAW,CAACxB,EAAQzC,GAAS,CAACA,EAAOyC,IACxEgC,EAAiBC,GAAkBT,EAAW,CAAC7C,EAAKlB,GAAQ,CAACA,EAAMkB,GAE1E,KAAI8C,EAAcO,GAAoBP,EAAcG,EAAgBE,GAGpE,IAAK,MAAMrL,KAAK8K,EAAU,CAEtB,MAAMW,EAAarB,GAAeW,EAAW,IAAM,KAAO/K,GAC1D,KAAIyL,EAAaD,GAAmBC,EAAaL,EAAeE,GAMhE,OAFA1B,EAAOxB,MAAM8C,GAAiBO,EAAavB,EAAOgB,GAAvB,KAC3BtB,EAAOxB,MAAM6C,GAAkBD,EAAcd,EAAOe,GAAxB,KACpBtJ,EAAI3B,GAOpB,OAFA4J,EAAOxB,MAAMpB,KAAO+C,EACpBH,EAAOxB,MAAMF,IAAM8B,EACZ,M,wHCxII,MAAM0B,EA8FjBC,YAAYtF,GAAK,8BA/BK,GA+BL,kBA5BP,GA4BO,kBAzBN,MAyBM,eAxBT,MAwBS,gBArBRrB,KAqBQ,oBApBJA,KAoBI,uBAnBD,IAmBC,8BAfM,MAeN,wBAZA,CACb4G,KAAM,GACNC,KAAM,GACNC,KAAM,GACNC,KAAM,GACNC,MAAO,GACPC,OAAQ,GACRC,WAAY,GACZC,OAAQ,GACRC,aAAc,KAMdC,KAAKhQ,QAAUgK,EAAMjL,OAAOkL,OAAO,IAAIoF,EAAMY,iBAAkBjG,GAE/D,MAAM,SAACkG,EAAD,WAAWC,EAAX,MAAuBC,EAAvB,QAA8BC,EAA9B,YAAuCC,EAAvC,QAAoDC,GAAWvG,EAEjE,CAAC,OAAQ,YAAYwG,SAASJ,KAAWC,IACzCrG,EAAIqG,QAAU,KAIbF,EAAWM,cACZN,EAAWM,YAAc,IAI7B,MAAM,QAACC,EAAD,QAAUC,EAAV,IAAmBC,EAAnB,QAAwBC,GAAWV,EACzCA,EAAWQ,SAAYL,GAAeK,EACtCR,EAAWU,QAAUA,GAAWH,GAAWC,GAAWC,EAGtDZ,KAAKc,YACLd,KAAKe,mBACLf,KAAKgB,cACLhB,KAAKiB,cAGDf,GAAYA,EAAS5N,QACrB4N,EAASzD,SAAQyE,GAASlB,KAAKmB,UAAUD,KAI7C,MAAM,OAACE,EAAD,IAASC,GAAOrB,KAAKsB,MAC3BtB,KAAKuB,SDqBmC,EAACjE,EAAmDC,EAAsBvN,KAEpH,MAAIwR,EAA4D,iBAAdlE,GAA4BA,aAAqBmE,YACrF,CAAEnE,YAAWC,YAAWvN,GAApCsN,EACF,MAAK,CAKHoE,OAAO1R,EAAmCwR,GACtC,MAAMlE,UAAEA,EAASC,OAAEA,GAAWxO,OAAOkL,OAAOuH,EAAaxR,GACzD,IAAKuN,IAAWD,EACZ,MAAM,IAAIqE,MAAM,yCAEpB,OAAOtE,EAAWC,EAAWC,EAAQiE,MCnCzBI,CAAaR,EAAQC,EAAK,CACtCjE,OAAQmD,IAIZa,EAAOS,aAAa,OAAQ,UAC5BT,EAAOS,aAAa,aAAc7B,KAAK8B,GAAG,eAG1C,MAAM9I,EAAOgH,KACbA,KAAK+B,qBAAuBC,uBAAuB,SAASC,IAGxD,IAAKZ,EAAIrF,YACL,OAAOgG,sBAAsBC,GAIjCjJ,EAAKkJ,SAASlI,EAAImI,SAClBnJ,EAAKoJ,uBAGDpI,EAAIqI,wBACJrJ,EAAKsJ,gBAAkBtI,EAAIqI,sBAC3BrJ,EAAKuJ,uBAAuBvJ,EAAKsJ,kBAIjCtI,EAAIwI,YACJxJ,EAAK0G,OAIT1G,EAAKyJ,qBAAsB,EAC3BzJ,EAAK0J,MAAM,WAQnB5B,YACI,MAAM,QAAC9Q,GAAWgQ,KAGlB,IAAK,MAAM5H,IAAQ,CAAC,KAAM,aACtBpI,EAAQoI,GAAQ+C,EAAiBnL,EAAQoI,IAK7C4H,KAAKsB,MF1Lb,CAAeqB,IAEX,MAAM,WACFxC,EADE,YAEFyC,EAFE,OAGFC,EAHE,SAIFC,EAJE,MAKF1C,EALE,YAMFE,GACAqC,EAAS3S,QAGP+S,EAASjR,GAAOA,EAAM,GAAK,8BAC3B0D,EAAIjE,GAAOoR,EAASb,GAAGvQ,GAEvBnD,EAAO+M,EAAsB,sDAG7ByH,EAAc,GAAK,gHAEYE,GAAY,mBAAmB1C,MAAUyC,EAAS,0BAA4B,kBAAkBrN,EAAE,sEACpGuN,EAAO5C,EAAWU,wEACGkC,EAAO5C,EAAWO,sGACYlL,EAAE,uUAMXA,EAAE,uHAG3BuN,EAAO5C,EAAWS,2JAEapL,EAAE,sHAG7BuN,EAAO5C,EAAWQ,mKAEanL,EAAE,qHAIxD2K,EAAWU,QAAU,GAAK,mGAEHkC,EAAOhU,OAAOsD,KAAK8N,EAAWM,aAAanO,gGAClByQ,EAAO5C,EAAWM,YAAYuC,sBAAsBxN,EAAE,iGAE9D8K,EAAc,MAAQ,yBAAyByC,EAAO5C,EAAWM,YAAY1J,qFAC7EuJ,EAAc,MAAQ,yBAAyByC,EAAO5C,EAAWM,YAAY9I,sFAC7E2I,EAAc,MAAQ,yBAAyByC,EAAO5C,EAAWM,YAAY7I,sFAC7E0I,EAAc,MAAQ,yBAAyByC,EAAO5C,EAAWM,YAAY5I,0GACzDkL,EAAO5C,EAAWM,YAAY/I,oEAEtElC,EAAE,8BAA8BuN,EAAO5C,EAAWM,YAAYjB,qBAAqBhK,EAAE,kFACjFA,EAAE,gCAAgCuN,EAAO5C,EAAWM,YAAYX,uBAAuBtK,EAAE,kFAC3FA,EAAE,+BAA+BuN,EAAO5C,EAAWM,YAAYd,sBAAsBnK,EAAE,6EAMxIyN,EAAM7U,EAAKqS,YAOjB,OAJAwC,EAAIjT,QAAQkT,MAAKpU,IAAMA,EAAEiU,SAAWjU,EAAE4N,UAAUyG,IAAI,YAGpDF,EAAI7K,KAAO,IAAM6K,EAAIjT,QAAQkT,MAAKrP,GAAKA,EAAE6I,UAAU0G,SAAS,YACrDhV,GEqHUiV,CAAWrD,MAGpBhQ,EAAQ4S,cACR5C,KAAKsB,MAAMF,OAASpR,EAAQM,IAGhCN,EAAQwN,UAAU8F,YAAYtD,KAAKsB,MAAMlT,MAG7C6S,cACI,MAAMjH,EAAMgG,KAAKhQ,QACX5B,EAAO4R,KAAKsB,MAKlB,GAFAtH,EAAIwD,UAAU+F,YAAYnV,EAAKA,MAE3B4L,EAAI6I,OAAQ,CACZ,MAAMW,EAASxJ,EAAI1J,GAAGsC,cAElBoH,EAAI1J,GAAGmT,YACPD,EAAOE,aAAatV,EAAKiT,IAAKrH,EAAI1J,GAAGmT,aAErCD,EAAOF,YAAYlV,EAAKiT,UAG5BrH,EAAIwD,UAAU8F,YAAYlV,EAAKiT,KAI9BrH,EAAI4I,YAIE5I,EAAI6I,QACX7I,EAAI1J,GAAGqT,SAFP3J,EAAI1J,GAAGsT,WAAWC,aAAazV,EAAKA,KAAM4L,EAAI1J,IAM9C0J,EAAI8J,UACJ9D,KAAK+D,UAIJ/J,EAAIgK,aACL5V,EAAKgT,OAAOrF,MAAMkI,WAAa,OAE1BjK,EAAI4I,cACLxU,EAAKsS,QAAQwD,UAAUnI,MAAMkI,WAAa,SAIlDjE,KAAKP,OAGTsB,mBAGI,MAAMoD,EAAOnE,KACPoE,EAAKpE,KAAKhQ,QAAQmQ,WAClBE,GAAW8D,EAAKnU,QAAQqQ,SAAW,KAAKgE,OAAO,IAC9CC,EAAIC,GAAMlE,EAAQrJ,MAAM,YAAcqJ,EAAU,GAGjDmE,EAAW,IACbxE,KAAKyE,SAAWzE,KAAKyE,OAASzE,KAAK0E,WAAW7K,SAE5CsG,EAAa,CAEfU,QAAS9G,EAAS,CACdnI,QAASuS,EAAK7C,MAAMT,QAAQ8D,OAC5BpK,QAAS4J,EAAK7C,MAAMT,QAAQA,QAE5BzG,OAAQ,IAAM+J,EAAKzB,MAAM,aAAc,SAAUyB,GACjDhK,SAASa,EAAGvE,GACR,IAAK2N,EAAGvD,QACJ,OAGJ,MAAMK,EAAQsD,KACR,MAAClD,EAAD,QAAQtR,GAAWmU,GACnB,UAACD,EAAD,aAAYU,GAAgBtD,EAAMZ,QAGpCyD,EAAKU,UAGL3D,EAAM9L,EAAQ,IAAJ4F,EAGVkG,EAAMvN,EAAI,IAAU,IAAJ8C,EAGhByK,EAAMvN,EAAI,IAAIuN,EAAMvN,EAAI,GACxBwQ,EAAKW,cAAc,WAIvB,MAAMC,EAAgB7D,EAAM9H,SAASF,SAAS,GAC9C8G,KAAKpO,QAAQmK,MAAMiJ,WAAaD,EAChC/E,KAAKzF,QAAQwB,MAAMiJ,WAAc,mEACW9D,EAAM5N,4EACd4N,EAAM/L,iBAAiB+L,EAAM5N,2BAA2B4N,EAAM5N,4BAI7FtD,EAAQgU,WAKDhU,EAAQ4S,aAAgBuB,EAAKO,YAGrCR,EAAUnI,MAAMkJ,YAAY,cAAeF,IAP3CzD,EAAMF,OAAOrF,MAAMkJ,YAAY,cAAeF,GAG9CzD,EAAMF,OAAO1E,UAAUiH,OAAO,UAQlC,MAAM7L,EAAOoJ,EAAM1H,SAASN,WAC5B,IAAK,MAAM,GAAC5I,EAAD,MAAK4Q,KAAUiD,EAAKe,cAC3B5U,EAAGoM,UAAU5E,IAASoJ,EAAM1H,SAASN,WAAa,MAAQ,UAAU,cAIxE0L,EAAa7I,MAAMkJ,YAAY,cAAeF,MAItDnE,IAAK7G,EAAS,CACVG,KAAa,MAAPqK,EAAa,IAAM,IACzB3S,QAASuS,EAAK7C,MAAMV,IAAI+D,OACxBpK,QAAS4J,EAAK7C,MAAMV,IAAIuE,OAExB/K,OAAQ,IAAM+J,EAAKzB,MAAM,aAAc,SAAUyB,GACjDhK,SAASxG,GACL,IAAKyQ,EAAGxD,MAAQwD,EAAGvD,QACf,OAGJ,MAAMK,EAAQsD,IAGVL,EAAKU,UACL3D,EAAM/L,EAAQ,IAAJxB,GAIdqM,KAAKpO,QAAQmK,MAAMqJ,gBAAmB,OAAMlE,EAAM/L,gBAClDgL,EAAWU,QAAQzE,aAI3BuE,QAAS5G,EAAS,CACdG,KAAa,MAAPoK,EAAa,IAAM,IACzB1S,QAASuS,EAAK7C,MAAMX,QAAQgE,OAC5BpK,QAAS4J,EAAK7C,MAAMX,QAAQwE,OAE5B/K,OAAQ,IAAM+J,EAAKzB,MAAM,aAAc,SAAUyB,GACjDhK,SAASxG,GACL,IAAKyQ,EAAGzD,UAAYyD,EAAGvD,QACnB,OAGJ,MAAMK,EAAQsD,IAGVL,EAAKU,UACL3D,EAAM5N,EAAI2B,KAAKD,MAAU,IAAJrB,GAAW,KAIpCqM,KAAKpO,QAAQmK,MAAMiJ,WAAc,iBAAgB9D,EAAM5N,KACvD6M,EAAWU,QAAQzE,aAI3BiJ,WAAY/I,EAAW,CACnBzM,SAAUsU,EAAK7C,MAAMb,YAAYzQ,QACjCuM,UAAW,SAEXpC,SAAStG,GACLsQ,EAAK7B,gBAAkBzO,EAAElB,OAAOjB,aAAa,aAAakI,cAC1DuK,EAAKU,SAAWV,EAAKW,cAAc,cAK/C9E,KAAKsF,YAAcnF,EAGvBa,cACI,MAAM,MAACM,EAAD,QAAQtR,GAAWgQ,KAEnBuF,EAAgB,CAGlBpK,EAAKmG,EAAMb,YAAYd,MAAO,SAAS,IAAMK,KAAKwF,gBAGlDrK,EAAK,CACDmG,EAAMb,YAAYX,OAClBwB,EAAMZ,QAAQwD,WACf,SAAS,KACRlE,KAAKyF,YAAYzF,KAAK0E,YAAc1E,KAAKyE,QAAQxL,UAAU,GAC3D+G,KAAK0C,MAAM,aAIfvH,EAAKmG,EAAMb,YAAYjB,KAAM,SAAS,MACjCQ,KAAK0F,eAAiB1V,EAAQwS,YAAcxC,KAAKP,UAItDtE,EAAKmG,EAAMb,YAAYkF,OAAQ,CAAC,QAAS,UAAU9R,IAG3CmM,KAAKkC,SAASrO,EAAElB,OAAOjD,OAAO,KAAUsQ,KAAKyC,sBAC7CzC,KAAK0C,MAAM,SAAU1C,KAAKyE,OAAQ,QAASzE,MAC3CA,KAAK0C,MAAM,aAAc,QAAS1C,OAGtCnM,EAAE+R,8BAINzK,EAAKmG,EAAMb,YAAYkF,OAAQ,CAAC,QAAS,SAAS9R,IAC9CmM,KAAK6E,QAAqB,SAAXhR,EAAEuE,KACjB4H,KAAK6E,SAAW7E,KAAK8E,cAAc,SAIvC3J,EAAK,CACDmG,EAAMT,QAAQA,QACdS,EAAMT,QAAQ8D,OACdrD,EAAMV,IAAIuE,OACV7D,EAAMV,IAAI+D,OACVrD,EAAMX,QAAQwE,OACd7D,EAAMX,QAAQgE,QACf,CAAC,YAAa,eAAe,IAAM3E,KAAK6E,SAAU,GAAM,CAACjQ,SAAS,KAIzE,IAAK5E,EAAQwS,WAAY,CACrB,MAAMqD,EAAK7V,EAAQ8V,aAEnBP,EAAcnT,KAGV+I,EAAKmG,EAAMF,OAAQ,SAAS,IAAMpB,KAAK+F,SAAW/F,KAAKP,OAASO,KAAKN,SAGrEvE,EAAKlK,SAAU,SAAS4C,GAAKmM,KAAK+F,WAAalS,EAAEhF,MAAQgX,GAAMhS,EAAEmS,OAASH,IAAO7F,KAAKP,SAGtFtE,EAAKlK,SAAU,CAAC,aAAc,cAAc4C,IACpCmM,KAAK+F,WAAa5K,EAAYtH,GAAGoS,MAAK3V,GAAMA,IAAOgR,EAAMD,KAAO/Q,IAAOgR,EAAMF,UAC7EpB,KAAKP,SAEV,CAACjP,SAAS,KAKrB,GAAIR,EAAQkW,kBAAmB,CAC3B,MAAMC,EAAS,CACXxO,KAAM,CAAC,IAAK,IAAK,IAAK,GACtBE,KAAM,CAAC,IAAK,IAAK,IAAK,GACtBD,KAAM,CAAC,IAAK,IAAK,IAAK,GACtBF,KAAM,CAAC,IAAK,IAAK,IAAK,MAG1ByD,EAAyBmG,EAAMb,YAAYkF,QAAQ,CAAC7W,EAAGsX,EAAMjS,KACzD,MAAMkS,EAAQF,EAAOnG,KAAKsG,yBAAyBlP,eAEnD,GAAIiP,EAAO,CACP,MAAMvR,EAAMuR,EAAMlS,GAGZoS,EAAKzX,GAAKgG,GAAO,IAAa,IAAPsR,EAAcA,GAG3C,OAAOG,GAAM,EAAI,EAAIxS,QAAQwS,EAAKzR,EAAMyR,EAAKzR,GAAK0R,YAAY,IAGlE,OAAO1X,KAIf,GAAIkB,EAAQyW,iBAAmBzW,EAAQ6S,OAAQ,CAC3C,IAAI6D,EAAU,KACd,MAAM1N,EAAOgH,KAGbuF,EAAcnT,KACV+I,EAAKtI,OAAQ,CAAC,SAAU,WAAW,KAC3BmG,EAAK+M,WAED/V,EAAQ2W,eACR3N,EAAKyG,OAGO,OAAZiH,GACAA,EAAUE,YAAW,IAAMF,EAAU,MAAM,KAG3C1E,uBAAsB,SAAS6E,IAC3B7N,EAAKoJ,uBACQ,OAAZsE,GAAqB1E,sBAAsB6E,QAGhDC,aAAaJ,GACbA,EAAUE,YAAW,IAAMF,EAAU,MAAM,SAGpD,CAAClW,SAAS,KAKrBwP,KAAK+G,eAAiBxB,EAG1BnD,uBACI,MAAM,QAACpS,GAAWgQ,KAGlB,IAAKhQ,EAAQ6S,OAAQ,CAMjB,IALgB7C,KAAKuB,SAASzG,OAAO,CACjC0C,UAAWvM,SAAS+V,KAAKzL,wBACzB4B,SAAUnN,EAAQmN,WAGR,CACV,MAAM7M,EAAK0P,KAAKsB,MAAMD,IAChB4F,EAAK3W,EAAGiL,wBACdjL,EAAGyL,MAAMF,KAAUhJ,OAAOqU,YAAcD,EAAGnL,QAAU,EAArC,KAChBxL,EAAGyL,MAAMpB,MAAW9H,OAAOsU,WAAaF,EAAGrL,OAAS,EAAnC,OAK7BkJ,cAAcsC,GACV,MAAM,MAAC9F,EAAD,OAAQmD,EAAR,QAAgBzU,GAAWgQ,KAGjC,GAAIsB,EAAMb,YAAYrI,OAAQ,CAG1B,MAAMxI,EAAU,KAAI0R,EAAMb,YAAYrI,OAAO1G,aAAa,eAC1D4P,EAAMb,YAAYkF,OAAOjW,MAAkC,mBAAnB+U,EAAO7U,GAC3C6U,EAAO7U,KAAUsJ,SAASlJ,EAAQqX,iBAAmB,IAIxDrH,KAAKyC,qBAAuBzC,KAAK6E,SAClC7E,KAAK0C,MAAM,SAAU+B,EAAQ2C,EAAapH,MAIlDwF,YAAY8B,GAAS,GACjB,MAAM,MAAChG,EAAD,QAAQtR,GAAWgQ,KAGpBhQ,EAAQ4S,aACTtB,EAAMF,OAAOrF,MAAMkJ,YAAY,cAAe,uBAGlD3D,EAAMF,OAAO1E,UAAUyG,IAAI,SAEtBnT,EAAQwS,YACTxC,KAAKP,OAGTO,KAAK0E,WAAa,KACb1E,KAAKyC,qBAAwB6E,IAG9BtH,KAAK0C,MAAM,OAAQ,MACnB1C,KAAK0C,MAAM,UAInB6E,iBAAiBhW,GACb,MAAM,OAACgH,EAAD,KAASH,EAAT,EAAe9E,GAAK6D,EAAY5F,IAChC,YAAC+O,GAAeN,KAAKhQ,QACrBwX,OAA0BtP,IAAN5E,GAAyB,IAANA,EAQ7C,OAJIiF,GAA4B,IAAlBA,EAAOjG,SACjBiG,EAAO,QAAKL,GAGT,CACHK,QAAUA,GAAW+H,GAAekH,EAAsB,KAAOjP,EACjEH,QAIR0J,GAAGjT,GACC,OAAOmR,KAAKhQ,QAAQyX,KAAK5Y,IAAQwQ,EAAMqI,cAAc7Y,GAGzD6T,MAAMiF,KAAUC,GACZ5H,KAAK6H,eAAeF,GAAOlL,SAAQwF,GAAMA,KAAM2F,EAAM5H,QAGzDrP,GAAGgX,EAAO1F,GAEN,OADAjC,KAAK6H,eAAeF,GAAOvV,KAAK6P,GACzBjC,KAGXnP,IAAI8W,EAAO1F,GACP,MAAM6F,EAAa9H,KAAK6H,eAAeF,IAAU,GAC3CxT,EAAQ2T,EAAUC,QAAQ9F,GAMhC,OAJK9N,GACD2T,EAAUE,OAAO7T,EAAO,GAGrB6L,KAQXmB,UAAUD,GACN,MAAM,OAAC3I,GAAUyH,KAAKuH,iBAAiBrG,GAEvC,GAAI3I,EAAQ,CACR,MAAM,cAAC2M,EAAD,MAAgB5D,GAAStB,KACzBkB,EAAQvI,KAAaJ,GAGrBjI,EAAK6K,EACN,6CAA4C+F,EAAM9H,SAASF,SAAS,mBAAmB8G,KAAK8B,GAAG,oBAgBpG,OAZAR,EAAMpB,SAASoD,YAAYhT,GAC3B4U,EAAc9S,KAAK,CAAC9B,KAAI4Q,UAGxBlB,KAAK+G,eAAe3U,KAChB+I,EAAK7K,EAAI,SAAS,KACd0P,KAAKyF,WAAWvE,EAAMjI,UAAU,GAChC+G,KAAK0C,MAAM,eAAgBxB,GAC3BlB,KAAK0C,MAAM,SAAUxB,EAAO,SAAUlB,WAIvC,EAGX,OAAO,EAQXiI,aAAa9T,GACT,MAAM+T,EAAclI,KAAKkF,cAAc/Q,GAGvC,GAAI+T,EAAa,CACb,MAAM,GAAC5X,GAAM4X,EAKb,OAFAlI,KAAKsB,MAAMpB,SAASqD,YAAYjT,GAChC0P,KAAKkF,cAAc8C,OAAO7T,EAAO,IAC1B,EAGX,OAAO,EAGXuR,WAAW4B,GAAS,GAChB,MAAM,QAAC5G,EAAD,OAAUU,GAAUpB,KAAKsB,MAGzByD,EAAgB/E,KAAKyE,OAAOrL,SAASF,SAAS,GAmBpD,OAlBAwH,EAAQwD,UAAUnI,MAAMkJ,YAAY,cAAeF,GAG9C/E,KAAKhQ,QAAQ4S,aACdxB,EAAOrF,MAAMkJ,YAAY,cAAeF,GAI5C3D,EAAO1E,UAAUiH,OAAO,SAGxB3D,KAAK0E,WAAa1E,KAAKyE,OAAO5K,QAGzBmG,KAAKyC,qBAAwB6E,GAC9BtH,KAAK0C,MAAM,OAAQ1C,KAAKyE,QAGrBzE,KAMX3D,UAGI8L,qBAAqBnI,KAAK+B,sBAG1B/B,KAAK+G,eAAetK,SAAQmL,GAAQzM,KAASyM,KAG7C7Y,OAAOsD,KAAK2N,KAAKsF,aACZ7I,SAAQ5N,GAAOmR,KAAKsF,YAAYzW,GAAKwN,YAO9C+L,mBACIpI,KAAK3D,UACL,MAAM,KAACjO,EAAD,IAAOiT,GAAOrB,KAAKsB,MAGrBlT,EAAKwE,eACLxE,EAAKwE,cAAc2Q,YAAYnV,GAInCiT,EAAIzO,cAAc2Q,YAAYlC,GAI9BtS,OAAOsD,KAAK2N,MACPvD,SAAQ5N,GAAOmR,KAAKnR,GAAO,OAMpC4Q,OACI,QAAIO,KAAK+F,WACL/F,KAAKsB,MAAMD,IAAI3E,UAAUiH,OAAO,WAChC3D,KAAK0C,MAAM,SACJ,GASfhD,OACI,OAAKM,KAAKhQ,QAAQ8T,WAAa9D,KAAK+F,WAChC/F,KAAKsB,MAAMD,IAAI3E,UAAUyG,IAAI,WAC7BnD,KAAKoC,uBACLpC,KAAK0C,MAAM,OAAQ1C,KAAKyE,QACjBzE,MASf+F,SACI,OAAO/F,KAAKsB,MAAMD,IAAI3E,UAAU0G,SAAS,WAY7CqC,QAAQtQ,EAAI,IAAKC,EAAI,EAAGzB,EAAI,EAAGL,EAAI,EAAGgU,GAAS,GAG3C,MAAMe,EAASrI,KAAK6E,QAIpB,GAHA7E,KAAK6E,SAAU,EAGX1P,EAAI,GAAKA,EAAI,KAAOC,EAAI,GAAKA,EAAI,KAAOzB,EAAI,GAAKA,EAAI,KAAOL,EAAI,GAAKA,EAAI,EACzE,OAAO,EAIX0M,KAAKyE,OAAS9L,EAAUxD,EAAGC,EAAGzB,EAAGL,GAGjC,MAAM,IAACsN,EAAD,QAAMD,EAAN,QAAeE,GAAWb,KAAKsF,YAiBrC,OAhBA1E,EAAI9F,OAAQ3F,EAAI,KAChBwL,EAAQ7F,OAAOxH,GACfuN,EAAQ/F,OAAO1F,EAAI,IAAK,EAAKzB,EAAI,KAG5B2T,GACDtH,KAAK0F,aAIL2C,GACArI,KAAK8E,gBAIT9E,KAAK6E,QAAUwD,GACR,EAWXnG,SAASoG,EAAQhB,GAAS,GAGtB,GAAe,OAAXgB,EAEA,OADAtI,KAAKwF,YAAY8B,IACV,EAGX,MAAM,OAAC/O,EAAD,KAASH,GAAQ4H,KAAKuH,iBAAiBe,GAG7C,GAAI/P,EAAQ,CAGR,MAAMgQ,EAAQnQ,EAAKwB,eACb,QAAC5J,GAAWgQ,KAAKsB,MAAMb,YACvB9N,EAAS3C,EAAQkT,MAAK5S,GAAMA,EAAGoB,aAAa,eAAiB6W,IAGnE,GAAI5V,IAAWA,EAAOoQ,OAClB,IAAK,MAAMzS,KAAMN,EACbM,EAAGoM,UAAUpM,IAAOqC,EAAS,MAAQ,UAAU,UAKvD,QAAKqN,KAAKyF,WAAWlN,EAAQ+O,IAKtBtH,KAAKuC,uBAAuBgG,GAGvC,OAAO,EASXhG,uBAAuBnK,GAMnB,OAHAA,EAAOA,EAAKwB,gBAGHoG,KAAKsB,MAAMb,YAAYzQ,QAC3BkT,MAAKvP,GAAKA,EAAEjC,aAAa,aAAauJ,WAAW7C,KAAUzE,EAAE6U,UAOtElC,yBACI,OAAOtG,KAAKsC,gBAMhBkC,WACI,OAAOxE,KAAKyE,OAOhBgE,mBACI,OAAOzI,KAAK0E,WAMhBgE,UACI,OAAO1I,KAAKsB,MAMhByC,UAII,OAHA/D,KAAKP,OACLO,KAAKhQ,QAAQ8T,UAAW,EACxB9D,KAAKsB,MAAMF,OAAO1E,UAAUyG,IAAI,YACzBnD,KAMX2I,SAGI,OAFA3I,KAAKhQ,QAAQ8T,UAAW,EACxB9D,KAAKsB,MAAMF,OAAO1E,UAAUiH,OAAO,YAC5B3D,M,SA74BMX,E,QAGFlE,G,EAHEkE,E,UAMAuJ,S,EANAvJ,E,gBASM,CAGnB,YAAa,sBACb,aAAc,6BACd,aAAc,eACd,iBAAkB,qBAClB,WAAY,OACZ,aAAc,SACd,YAAa,QAGb,gBAAiB,iBACjB,kBAAmB,mBACnB,iBAAkB,kBAClB,aAAc,oBACd,eAAgB,uBAChB,WAAY,uBACZ,eAAgB,qB,EA3BHA,E,kBA+BQ,CACrByD,SAAU,KACV1C,MAAO,UACPwC,aAAa,EACbrC,QAAS,EACTuD,UAAU,EACVE,YAAY,EACZ2C,eAAe,EACfU,gBAAiB,EACjB/G,aAAa,EACbmG,gBAAgB,EAChBjJ,UAAW,OAEX2C,WAAY,CACRM,YAAa,IAGjBgH,KAAM,GACNvH,SAAU,KACV2C,QAAQ,EACRxC,QAAS,KAET8B,QAAS,UACTE,sBAAuB,KACvBlF,SAAU,gBACV+I,mBAAmB,EACnB1D,YAAY,EAEZsD,aAAc,W,EA3DDzG,E,UAuKDrP,GAAW,IAAIqP,EAAMrP,K", "file": "pickr.min.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Pickr\"] = factory();\n\telse\n\t\troot[\"Pickr\"] = factory();\n})(self, function() {\nreturn ", "// The require scope\nvar __webpack_require__ = {};\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "/* eslint-disable prefer-rest-params */\r\nfunction eventListener(method, elements, events, fn, options = {}) {\r\n\r\n    // Normalize array\r\n    if (elements instanceof HTMLCollection || elements instanceof NodeList) {\r\n        elements = Array.from(elements);\r\n    } else if (!Array.isArray(elements)) {\r\n        elements = [elements];\r\n    }\r\n\r\n    if (!Array.isArray(events)) {\r\n        events = [events];\r\n    }\r\n\r\n    for (const el of elements) {\r\n        for (const ev of events) {\r\n            el[method](ev, fn, {capture: false, ...options});\r\n        }\r\n    }\r\n\r\n    return Array.prototype.slice.call(arguments, 1);\r\n}\r\n\r\n/**\r\n * Add event(s) to element(s).\r\n * @param elements DOM-Elements\r\n * @param events Event names\r\n * @param fn Callback\r\n * @param options Optional options\r\n * @return Array passed arguments\r\n */\r\nexport const on = eventListener.bind(null, 'addEventListener');\r\n\r\n/**\r\n * Remove event(s) from element(s).\r\n * @param elements DOM-Elements\r\n * @param events Event names\r\n * @param fn Callback\r\n * @param options Optional options\r\n * @return Array passed arguments\r\n */\r\nexport const off = eventListener.bind(null, 'removeEventListener');\r\n\r\n/**\r\n * Creates an DOM-Element out of a string (Single element).\r\n * @param html HTML representing a single element\r\n * @returns {Element | null} The element.\r\n */\r\nexport function createElementFromString(html) {\r\n    const div = document.createElement('div');\r\n    div.innerHTML = html.trim();\r\n    return div.firstElementChild;\r\n}\r\n\r\n/**\r\n * Creates a new html element, every element which has\r\n * a ':ref' attribute will be saved in a object (which will be returned)\r\n * where the value of ':ref' is the object-key and the value the HTMLElement.\r\n *\r\n * It's possible to create a hierarchy if you add a ':obj' attribute. Every\r\n * sibling will be added to the object which will get the name from the 'data-con' attribute.\r\n *\r\n * If you want to create an Array out of multiple elements, you can use the ':arr' attribute,\r\n * the value defines the key and all elements, which has the same parent and the same 'data-arr' attribute,\r\n * would be added to it.\r\n *\r\n * @param str - The HTML String.\r\n */\r\n\r\nexport function createFromTemplate(str) {\r\n\r\n    // Removes an attribute from a HTMLElement and returns the value.\r\n    const removeAttribute = (el, name) => {\r\n        const value = el.getAttribute(name);\r\n        el.removeAttribute(name);\r\n        return value;\r\n    };\r\n\r\n    // Recursive function to resolve template\r\n    const resolve = (element, base = {}) => {\r\n\r\n        // Check key and container attribute\r\n        const con = removeAttribute(element, ':obj');\r\n        const key = removeAttribute(element, ':ref');\r\n        const subtree = con ? (base[con] = {}) : base;\r\n\r\n        // Check and save element\r\n        key && (base[key] = element);\r\n        for (const child of Array.from(element.children)) {\r\n            const arr = removeAttribute(child, ':arr');\r\n            const sub = resolve(child, arr ? {} : subtree);\r\n\r\n            if (arr) {\r\n\r\n                // Check if there is already an array and add element\r\n                (subtree[arr] || (subtree[arr] = []))\r\n                    .push(Object.keys(sub).length ? sub : child);\r\n            }\r\n        }\r\n\r\n        return base;\r\n    };\r\n\r\n    return resolve(createElementFromString(str));\r\n}\r\n\r\n/**\r\n * Polyfill for safari & firefox for the eventPath event property.\r\n * @param evt The event object.\r\n * @return [String] event path.\r\n */\r\nexport function eventPath(evt) {\r\n    let path = evt.path || (evt.composedPath && evt.composedPath());\r\n    if (path) {\r\n        return path;\r\n    }\r\n\r\n    let el = evt.target.parentElement;\r\n    path = [evt.target, el];\r\n    while (el = el.parentElement) {\r\n        path.push(el);\r\n    }\r\n\r\n    path.push(document, window);\r\n    return path;\r\n}\r\n\r\n/**\r\n * Resolves a HTMLElement by query.\r\n * @param val\r\n * @returns {null|Document|Element}\r\n */\r\nexport function resolveElement(val) {\r\n    if (val instanceof Element) {\r\n        return val;\r\n    } else if (typeof val === 'string') {\r\n        return val.split(/>>/g).reduce((pv, cv, ci, a) => {\r\n            pv = pv.querySelector(cv);\r\n            return ci < a.length - 1 ? pv.shadowRoot : pv;\r\n        }, document);\r\n    }\r\n\r\n    return null;\r\n}\r\n\r\n/**\r\n * Creates the ability to change numbers in an input field with the scroll-wheel.\r\n * @param el\r\n * @param mapper\r\n */\r\nexport function adjustableInputNumbers(el, mapper = v => v) {\r\n\r\n    function handleScroll(e) {\r\n        const inc = ([0.001, 0.01, 0.1])[Number(e.shiftKey || e.ctrlKey * 2)] * (e.deltaY < 0 ? 1 : -1);\r\n\r\n        let index = 0;\r\n        let off = el.selectionStart;\r\n        el.value = el.value.replace(/[\\d.]+/g, (v, i) => {\r\n\r\n            // Check if number is in cursor range and increase it\r\n            if (i <= off && i + v.length >= off) {\r\n                off = i;\r\n                return mapper(Number(v), inc, index);\r\n            }\r\n\r\n            index++;\r\n            return v;\r\n        });\r\n\r\n        el.focus();\r\n        el.setSelectionRange(off, off);\r\n\r\n        // Prevent default and trigger input event\r\n        e.preventDefault();\r\n        el.dispatchEvent(new Event('input'));\r\n    }\r\n\r\n    // Bind events\r\n    on(el, 'focus', () => on(window, 'wheel', handleScroll, {passive: false}));\r\n    on(el, 'blur', () => off(window, 'wheel', handleScroll));\r\n}\r\n", "// Shorthands\r\nconst {min, max, floor, round} = Math;\r\n\r\n/**\r\n * Tries to convert a color name to rgb/a hex representation\r\n * @param name\r\n * @returns {string | CanvasGradient | CanvasPattern}\r\n */\r\nfunction standardizeColor(name) {\r\n\r\n    // Since invalid color's will be parsed as black, filter them out\r\n    if (name.toLowerCase() === 'black') {\r\n        return '#000';\r\n    }\r\n\r\n    const ctx = document.createElement('canvas').getContext('2d');\r\n    ctx.fillStyle = name;\r\n    return ctx.fillStyle === '#000' ? null : ctx.fillStyle;\r\n}\r\n\r\n/**\r\n * Convert HSV spectrum to RGB.\r\n * @param h Hue\r\n * @param s Saturation\r\n * @param v Value\r\n * @returns {number[]} Array with rgb values.\r\n */\r\nexport function hsvToRgb(h, s, v) {\r\n    h = (h / 360) * 6;\r\n    s /= 100;\r\n    v /= 100;\r\n\r\n    const i = floor(h);\r\n\r\n    const f = h - i;\r\n    const p = v * (1 - s);\r\n    const q = v * (1 - f * s);\r\n    const t = v * (1 - (1 - f) * s);\r\n\r\n    const mod = i % 6;\r\n    const r = [v, q, p, p, t, v][mod];\r\n    const g = [t, v, v, q, p, p][mod];\r\n    const b = [p, p, t, v, v, q][mod];\r\n\r\n    return [\r\n        r * 255,\r\n        g * 255,\r\n        b * 255\r\n    ];\r\n}\r\n\r\n/**\r\n * Convert HSV spectrum to Hex.\r\n * @param h Hue\r\n * @param s Saturation\r\n * @param v Value\r\n * @returns {string[]} Hex values\r\n */\r\nexport function hsvToHex(h, s, v) {\r\n    return hsvToRgb(h, s, v).map(v =>\r\n        round(v).toString(16).padStart(2, '0')\r\n    );\r\n}\r\n\r\n/**\r\n * Convert HSV spectrum to CMYK.\r\n * @param h Hue\r\n * @param s Saturation\r\n * @param v Value\r\n * @returns {number[]} CMYK values\r\n */\r\nexport function hsvToCmyk(h, s, v) {\r\n    const rgb = hsvToRgb(h, s, v);\r\n    const r = rgb[0] / 255;\r\n    const g = rgb[1] / 255;\r\n    const b = rgb[2] / 255;\r\n\r\n    const k = min(1 - r, 1 - g, 1 - b);\r\n    const c = k === 1 ? 0 : (1 - r - k) / (1 - k);\r\n    const m = k === 1 ? 0 : (1 - g - k) / (1 - k);\r\n    const y = k === 1 ? 0 : (1 - b - k) / (1 - k);\r\n\r\n    return [\r\n        c * 100,\r\n        m * 100,\r\n        y * 100,\r\n        k * 100\r\n    ];\r\n}\r\n\r\n/**\r\n * Convert HSV spectrum to HSL.\r\n * @param h Hue\r\n * @param s Saturation\r\n * @param v Value\r\n * @returns {number[]} HSL values\r\n */\r\nexport function hsvToHsl(h, s, v) {\r\n    s /= 100;\r\n    v /= 100;\r\n\r\n    const l = (2 - s) * v / 2;\r\n\r\n    if (l !== 0) {\r\n        if (l === 1) {\r\n            s = 0;\r\n        } else if (l < 0.5) {\r\n            s = s * v / (l * 2);\r\n        } else {\r\n            s = s * v / (2 - l * 2);\r\n        }\r\n    }\r\n\r\n    return [\r\n        h,\r\n        s * 100,\r\n        l * 100\r\n    ];\r\n}\r\n\r\n/**\r\n * Convert RGB to HSV.\r\n * @param r Red\r\n * @param g Green\r\n * @param b Blue\r\n * @return {number[]} HSV values.\r\n */\r\nfunction rgbToHsv(r, g, b) {\r\n    r /= 255;\r\n    g /= 255;\r\n    b /= 255;\r\n\r\n    const minVal = min(r, g, b);\r\n    const maxVal = max(r, g, b);\r\n    const delta = maxVal - minVal;\r\n\r\n    let h, s;\r\n    const v = maxVal;\r\n    if (delta === 0) {\r\n        h = s = 0;\r\n    } else {\r\n        s = delta / maxVal;\r\n        const dr = (((maxVal - r) / 6) + (delta / 2)) / delta;\r\n        const dg = (((maxVal - g) / 6) + (delta / 2)) / delta;\r\n        const db = (((maxVal - b) / 6) + (delta / 2)) / delta;\r\n\r\n        if (r === maxVal) {\r\n            h = db - dg;\r\n        } else if (g === maxVal) {\r\n            h = (1 / 3) + dr - db;\r\n        } else if (b === maxVal) {\r\n            h = (2 / 3) + dg - dr;\r\n        }\r\n\r\n        if (h < 0) {\r\n            h += 1;\r\n        } else if (h > 1) {\r\n            h -= 1;\r\n        }\r\n    }\r\n\r\n    return [\r\n        h * 360,\r\n        s * 100,\r\n        v * 100\r\n    ];\r\n}\r\n\r\n/**\r\n * Convert CMYK to HSV.\r\n * @param c Cyan\r\n * @param m Magenta\r\n * @param y Yellow\r\n * @param k Key (Black)\r\n * @return {number[]} HSV values.\r\n */\r\nfunction cmykToHsv(c, m, y, k) {\r\n    c /= 100;\r\n    m /= 100;\r\n    y /= 100;\r\n    k /= 100;\r\n\r\n    const r = (1 - min(1, c * (1 - k) + k)) * 255;\r\n    const g = (1 - min(1, m * (1 - k) + k)) * 255;\r\n    const b = (1 - min(1, y * (1 - k) + k)) * 255;\r\n\r\n    return [...rgbToHsv(r, g, b)];\r\n}\r\n\r\n/**\r\n * Convert HSL to HSV.\r\n * @param h Hue\r\n * @param s Saturation\r\n * @param l Lightness\r\n * @return {number[]} HSV values.\r\n */\r\nfunction hslToHsv(h, s, l) {\r\n    s /= 100;\r\n    l /= 100;\r\n    s *= l < 0.5 ? l : 1 - l;\r\n\r\n    const ns = (2 * s / (l + s)) * 100;\r\n    const v = (l + s) * 100;\r\n    return [h, isNaN(ns) ? 0 : ns, v];\r\n}\r\n\r\n/**\r\n * Convert HEX to HSV.\r\n * @param hex Hexadecimal string of rgb colors, can have length 3 or 6.\r\n * @return {number[]} HSV values.\r\n */\r\nfunction hexToHsv(hex) {\r\n    return rgbToHsv(...hex.match(/.{2}/g).map(v => parseInt(v, 16)));\r\n}\r\n\r\n/**\r\n * Try's to parse a string which represents a color to a HSV array.\r\n * Current supported types are cmyk, rgba, hsla and hexadecimal.\r\n * @param str\r\n * @return {*}\r\n */\r\nexport function parseToHSVA(str) {\r\n\r\n    // Check if string is a color-name\r\n    str = str.match(/^[a-zA-Z]+$/) ? standardizeColor(str) : str;\r\n\r\n    // Regular expressions to match different types of color represention\r\n    const regex = {\r\n        cmyk: /^cmyk[\\D]+([\\d.]+)[\\D]+([\\d.]+)[\\D]+([\\d.]+)[\\D]+([\\d.]+)/i,\r\n        rgba: /^((rgba)|rgb)[\\D]+([\\d.]+)[\\D]+([\\d.]+)[\\D]+([\\d.]+)[\\D]*?([\\d.]+|$)/i,\r\n        hsla: /^((hsla)|hsl)[\\D]+([\\d.]+)[\\D]+([\\d.]+)[\\D]+([\\d.]+)[\\D]*?([\\d.]+|$)/i,\r\n        hsva: /^((hsva)|hsv)[\\D]+([\\d.]+)[\\D]+([\\d.]+)[\\D]+([\\d.]+)[\\D]*?([\\d.]+|$)/i,\r\n        hexa: /^#?(([\\dA-Fa-f]{3,4})|([\\dA-Fa-f]{6})|([\\dA-Fa-f]{8}))$/i\r\n    };\r\n\r\n    /**\r\n     * Takes an Array of any type, convert strings which represents\r\n     * a number to a number an anything else to undefined.\r\n     * @param array\r\n     * @return {*}\r\n     */\r\n    const numarize = array => array.map(v => /^(|\\d+)\\.\\d+|\\d+$/.test(v) ? Number(v) : undefined);\r\n\r\n    let match;\r\n    invalid: for (const type in regex) {\r\n\r\n        // Check if current scheme passed\r\n        if (!(match = regex[type].exec(str))) {\r\n            continue;\r\n        }\r\n\r\n        // Match[2] does only contain a truly value if rgba, hsla, or hsla got matched\r\n        const alphaValid = a => (!!match[2] === (typeof a === 'number'));\r\n\r\n        // Try to convert\r\n        switch (type) {\r\n            case 'cmyk': {\r\n                const [, c, m, y, k] = numarize(match);\r\n\r\n                if (c > 100 || m > 100 || y > 100 || k > 100) {\r\n                    break invalid;\r\n                }\r\n\r\n                return {values: cmykToHsv(c, m, y, k), type};\r\n            }\r\n            case 'rgba': {\r\n                const [, , , r, g, b, a] = numarize(match);\r\n\r\n                if (r > 255 || g > 255 || b > 255 || a < 0 || a > 1 || !alphaValid(a)) {\r\n                    break invalid;\r\n                }\r\n\r\n                return {values: [...rgbToHsv(r, g, b), a], a, type};\r\n            }\r\n            case 'hexa': {\r\n                let [, hex] = match;\r\n\r\n                if (hex.length === 4 || hex.length === 3) {\r\n                    hex = hex.split('').map(v => v + v).join('');\r\n                }\r\n\r\n                const raw = hex.substring(0, 6);\r\n                let a = hex.substring(6);\r\n\r\n                // Convert 0 - 255 to 0 - 1 for opacity\r\n                a = a ? (parseInt(a, 16) / 255) : undefined;\r\n\r\n                return {values: [...hexToHsv(raw), a], a, type};\r\n            }\r\n            case 'hsla': {\r\n                const [, , , h, s, l, a] = numarize(match);\r\n\r\n                if (h > 360 || s > 100 || l > 100 || a < 0 || a > 1 || !alphaValid(a)) {\r\n                    break invalid;\r\n                }\r\n\r\n                return {values: [...hslToHsv(h, s, l), a], a, type};\r\n            }\r\n            case 'hsva': {\r\n                const [, , , h, s, v, a] = numarize(match);\r\n\r\n                if (h > 360 || s > 100 || v > 100 || a < 0 || a > 1 || !alphaValid(a)) {\r\n                    break invalid;\r\n                }\r\n\r\n                return {values: [h, s, v, a], a, type};\r\n            }\r\n        }\r\n    }\r\n\r\n    return {values: null, type: null};\r\n}\r\n", "import {hsvToCmyk, hsvToHex, hsvToHsl, hsvToRgb} from './color';\r\n\r\n/**\r\n * Simple class which holds the properties\r\n * of the color represention model hsla (hue saturation lightness alpha)\r\n */\r\nexport function HSVaColor(h = 0, s = 0, v = 0, a = 1) {\r\n    const mapper = (original, next) => (precision = -1) => {\r\n        return next(~precision ? original.map(v => Number(v.toFixed(precision))) : original);\r\n    };\r\n\r\n    const that = {\r\n        h, s, v, a,\r\n\r\n        toHSVA() {\r\n            const hsva = [that.h, that.s, that.v, that.a];\r\n            hsva.toString = mapper(hsva, arr => `hsva(${arr[0]}, ${arr[1]}%, ${arr[2]}%, ${that.a})`);\r\n            return hsva;\r\n        },\r\n\r\n        toHSLA() {\r\n            const hsla = [...hsvToHsl(that.h, that.s, that.v), that.a];\r\n            hsla.toString = mapper(hsla, arr => `hsla(${arr[0]}, ${arr[1]}%, ${arr[2]}%, ${that.a})`);\r\n            return hsla;\r\n        },\r\n\r\n        toRGBA() {\r\n            const rgba = [...hsvToRgb(that.h, that.s, that.v), that.a];\r\n            rgba.toString = mapper(rgba, arr => `rgba(${arr[0]}, ${arr[1]}, ${arr[2]}, ${that.a})`);\r\n            return rgba;\r\n        },\r\n\r\n        toCMYK() {\r\n            const cmyk = hsvToCmyk(that.h, that.s, that.v);\r\n            cmyk.toString = mapper(cmyk, arr => `cmyk(${arr[0]}%, ${arr[1]}%, ${arr[2]}%, ${arr[3]}%)`);\r\n            return cmyk;\r\n        },\r\n\r\n        toHEXA() {\r\n            const hex = hsvToHex(that.h, that.s, that.v);\r\n\r\n            // Check if alpha channel make sense, convert it to 255 number space, convert\r\n            // To hex and pad it with zeros if needet.\r\n            const alpha = that.a >= 1 ? '' : Number((that.a * 255).toFixed(0))\r\n                .toString(16)\r\n                .toUpperCase().padStart(2, '0');\r\n\r\n            alpha && hex.push(alpha);\r\n            hex.toString = () => `#${hex.join('').toUpperCase()}`;\r\n            return hex;\r\n        },\r\n\r\n        clone: () => HSVaColor(that.h, that.s, that.v, that.a)\r\n    };\r\n\r\n    return that;\r\n}\r\n", "import * as _ from '../utils/utils';\r\n\r\nconst clamp = v => Math.max(Math.min(v, 1), 0);\r\nexport default function Moveable(opt) {\r\n\r\n    const that = {\r\n\r\n        // Assign default values\r\n        options: Object.assign({\r\n            lock: null,\r\n            onchange: () => 0,\r\n            onstop: () => 0\r\n        }, opt),\r\n\r\n        _keyboard(e) {\r\n            const {options} = that;\r\n            const {type, key} = e;\r\n\r\n            // Check to see if the Movable is focused and then move it based on arrow key inputs\r\n            // For improved accessibility\r\n            if (document.activeElement === options.wrapper) {\r\n                const {lock} = that.options;\r\n                const up = key === 'ArrowUp';\r\n                const right = key === 'ArrowRight';\r\n                const down = key === 'ArrowDown';\r\n                const left = key === 'ArrowLeft';\r\n\r\n                if (type === 'keydown' && (up || right || down || left)) {\r\n                    let xm = 0;\r\n                    let ym = 0;\r\n\r\n                    if (lock === 'v') {\r\n                        xm = (up || right) ? 1 : -1;\r\n                    } else if (lock === 'h') {\r\n                        xm = (up || right) ? -1 : 1;\r\n                    } else {\r\n                        ym = up ? -1 : (down ? 1 : 0);\r\n                        xm = left ? -1 : (right ? 1 : 0);\r\n                    }\r\n\r\n                    that.update(\r\n                        clamp(that.cache.x + (0.01 * xm)),\r\n                        clamp(that.cache.y + (0.01 * ym))\r\n                    );\r\n                    e.preventDefault();\r\n                } else if (key.startsWith('Arrow')) {\r\n                    that.options.onstop();\r\n                    e.preventDefault();\r\n                }\r\n            }\r\n        },\r\n\r\n        _tapstart(evt) {\r\n            _.on(document, ['mouseup', 'touchend', 'touchcancel'], that._tapstop);\r\n            _.on(document, ['mousemove', 'touchmove'], that._tapmove);\r\n\r\n            if (evt.cancelable) {\r\n                evt.preventDefault();\r\n            }\r\n\r\n            // Trigger\r\n            that._tapmove(evt);\r\n        },\r\n\r\n        _tapmove(evt) {\r\n            const {options, cache} = that;\r\n            const {lock, element, wrapper} = options;\r\n            const b = wrapper.getBoundingClientRect();\r\n\r\n            let x = 0, y = 0;\r\n            if (evt) {\r\n                const touch = evt && evt.touches && evt.touches[0];\r\n                x = evt ? (touch || evt).clientX : 0;\r\n                y = evt ? (touch || evt).clientY : 0;\r\n\r\n                // Reset to bounds\r\n                if (x < b.left) {\r\n                    x = b.left;\r\n                } else if (x > b.left + b.width) {\r\n                    x = b.left + b.width;\r\n                }\r\n                if (y < b.top) {\r\n                    y = b.top;\r\n                } else if (y > b.top + b.height) {\r\n                    y = b.top + b.height;\r\n                }\r\n\r\n                // Normalize\r\n                x -= b.left;\r\n                y -= b.top;\r\n            } else if (cache) {\r\n                x = cache.x * b.width;\r\n                y = cache.y * b.height;\r\n            }\r\n\r\n            if (lock !== 'h') {\r\n                element.style.left = `calc(${x / b.width * 100}% - ${element.offsetWidth / 2}px)`;\r\n            }\r\n\r\n            if (lock !== 'v') {\r\n                element.style.top = `calc(${y / b.height * 100}% - ${element.offsetHeight / 2}px)`;\r\n            }\r\n\r\n            that.cache = {x: x / b.width, y: y / b.height};\r\n            const cx = clamp(x / b.width);\r\n            const cy = clamp(y / b.height);\r\n\r\n            switch (lock) {\r\n                case 'v':\r\n                    return options.onchange(cx);\r\n                case 'h':\r\n                    return options.onchange(cy);\r\n                default:\r\n                    return options.onchange(cx, cy);\r\n            }\r\n        },\r\n\r\n        _tapstop() {\r\n            that.options.onstop();\r\n            _.off(document, ['mouseup', 'touchend', 'touchcancel'], that._tapstop);\r\n            _.off(document, ['mousemove', 'touchmove'], that._tapmove);\r\n        },\r\n\r\n        trigger() {\r\n            that._tapmove();\r\n        },\r\n\r\n        update(x = 0, y = 0) {\r\n            const {left, top, width, height} = that.options.wrapper.getBoundingClientRect();\r\n\r\n            if (that.options.lock === 'h') {\r\n                y = x;\r\n            }\r\n\r\n            that._tapmove({\r\n                clientX: left + width * x,\r\n                clientY: top + height * y\r\n            });\r\n        },\r\n\r\n        destroy() {\r\n            const {options, _tapstart, _keyboard} = that;\r\n            _.off(document, ['keydown', 'keyup'], _keyboard);\r\n            _.off([options.wrapper, options.element], 'mousedown', _tapstart);\r\n            _.off([options.wrapper, options.element], 'touchstart', _tapstart, {\r\n                passive: false\r\n            });\r\n        }\r\n    };\r\n\r\n    // Initilize\r\n    const {options, _tapstart, _keyboard} = that;\r\n    _.on([options.wrapper, options.element], 'mousedown', _tapstart);\r\n    _.on([options.wrapper, options.element], 'touchstart', _tapstart, {\r\n        passive: false\r\n    });\r\n\r\n    _.on(document, ['keydown', 'keyup'], _keyboard);\r\n\r\n    return that;\r\n}\r\n", "import * as _ from '../utils/utils';\n\nexport default function Selectable(opt = {}) {\n    opt = Object.assign({\n        onchange: () => 0,\n        className: '',\n        elements: []\n    }, opt);\n\n    const onTap = _.on(opt.elements, 'click', evt => {\n        opt.elements.forEach(e =>\n            e.classList[evt.target === e ? 'add' : 'remove'](opt.className)\n        );\n\n        opt.onchange(evt);\n\n        // Fix for https://github.com/Simonwep/pickr/issues/243\n        evt.stopPropagation();\n    });\n\n    return {\n        destroy: () => _.off(...onTap)\n    };\n}\n", "import * as _ from './utils/utils';\r\n\r\nexport default instance => {\r\n\r\n    const {\r\n        components,\r\n        useAsButton,\r\n        inline,\r\n        appClass,\r\n        theme,\r\n        lockOpacity\r\n    } = instance.options;\r\n\r\n    // Utils\r\n    const hidden = con => con ? '' : 'style=\"display:none\" hidden';\r\n    const t = str => instance._t(str);\r\n\r\n    const root = _.createFromTemplate(`\r\n      <div :ref=\"root\" class=\"pickr\">\r\n\r\n        ${useAsButton ? '' : '<button type=\"button\" :ref=\"button\" class=\"pcr-button\"></button>'}\r\n\r\n        <div :ref=\"app\" class=\"pcr-app ${appClass || ''}\" data-theme=\"${theme}\" ${inline ? 'style=\"position: unset\"' : ''} aria-label=\"${t('ui:dialog', 'color picker dialog')}\" role=\"window\">\r\n          <div class=\"pcr-selection\" ${hidden(components.palette)}>\r\n            <div :obj=\"preview\" class=\"pcr-color-preview\" ${hidden(components.preview)}>\r\n              <button type=\"button\" :ref=\"lastColor\" class=\"pcr-last-color\" aria-label=\"${t('btn:last-color')}\"></button>\r\n              <div :ref=\"currentColor\" class=\"pcr-current-color\"></div>\r\n            </div>\r\n\r\n            <div :obj=\"palette\" class=\"pcr-color-palette\">\r\n              <div :ref=\"picker\" class=\"pcr-picker\"></div>\r\n              <div :ref=\"palette\" class=\"pcr-palette\" tabindex=\"0\" aria-label=\"${t('aria:palette')}\" role=\"listbox\"></div>\r\n            </div>\r\n\r\n            <div :obj=\"hue\" class=\"pcr-color-chooser\" ${hidden(components.hue)}>\r\n              <div :ref=\"picker\" class=\"pcr-picker\"></div>\r\n              <div :ref=\"slider\" class=\"pcr-hue pcr-slider\" tabindex=\"0\" aria-label=\"${t('aria:hue')}\" role=\"slider\"></div>\r\n            </div>\r\n\r\n            <div :obj=\"opacity\" class=\"pcr-color-opacity\" ${hidden(components.opacity)}>\r\n              <div :ref=\"picker\" class=\"pcr-picker\"></div>\r\n              <div :ref=\"slider\" class=\"pcr-opacity pcr-slider\" tabindex=\"0\" aria-label=\"${t('aria:opacity', 'opacity selection slider')}\" role=\"slider\"></div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"pcr-swatches ${components.palette ? '' : 'pcr-last'}\" :ref=\"swatches\"></div>\r\n\r\n          <div :obj=\"interaction\" class=\"pcr-interaction\" ${hidden(Object.keys(components.interaction).length)}>\r\n            <input :ref=\"result\" class=\"pcr-result\" type=\"text\" spellcheck=\"false\" ${hidden(components.interaction.input)} aria-label=\"${t('aria:input', 'color input field')}\">\r\n\r\n            <input :arr=\"options\" class=\"pcr-type\" data-type=\"HEXA\" value=\"${lockOpacity ? 'HEX' : 'HEXA'}\" type=\"button\" ${hidden(components.interaction.hex)}>\r\n            <input :arr=\"options\" class=\"pcr-type\" data-type=\"RGBA\" value=\"${lockOpacity ? 'RGB' : 'RGBA'}\" type=\"button\" ${hidden(components.interaction.rgba)}>\r\n            <input :arr=\"options\" class=\"pcr-type\" data-type=\"HSLA\" value=\"${lockOpacity ? 'HSL' : 'HSLA'}\" type=\"button\" ${hidden(components.interaction.hsla)}>\r\n            <input :arr=\"options\" class=\"pcr-type\" data-type=\"HSVA\" value=\"${lockOpacity ? 'HSV' : 'HSVA'}\" type=\"button\" ${hidden(components.interaction.hsva)}>\r\n            <input :arr=\"options\" class=\"pcr-type\" data-type=\"CMYK\" value=\"CMYK\" type=\"button\" ${hidden(components.interaction.cmyk)}>\r\n\r\n            <input :ref=\"save\" class=\"pcr-save\" value=\"${t('btn:save')}\" type=\"button\" ${hidden(components.interaction.save)} aria-label=\"${t('aria:btn:save')}\">\r\n            <input :ref=\"cancel\" class=\"pcr-cancel\" value=\"${t('btn:cancel')}\" type=\"button\" ${hidden(components.interaction.cancel)} aria-label=\"${t('aria:btn:cancel')}\">\r\n            <input :ref=\"clear\" class=\"pcr-clear\" value=\"${t('btn:clear')}\" type=\"button\" ${hidden(components.interaction.clear)} aria-label=\"${t('aria:btn:clear')}\">\r\n          </div>\r\n        </div>\r\n      </div>\r\n    `);\r\n\r\n    const int = root.interaction;\r\n\r\n    // Select option which is not hidden\r\n    int.options.find(o => !o.hidden && !o.classList.add('active'));\r\n\r\n    // Append method to find currently active option\r\n    int.type = () => int.options.find(e => e.classList.contains('active'));\r\n    return root;\r\n};\r\n", "type Direction = 'top' | 'left' | 'bottom' | 'right';\n\nexport type VariantFlipOrder = {\n    start: string;\n    middle: string;\n    end: string;\n};\n\nexport type PositionFlipOrder = {\n    top: string;\n    right: string;\n    bottom: string;\n    left: string;\n};\n\nexport type NanoPopPosition =\n    'top-start' | 'top-middle' | 'top-end' |\n    'left-start' | 'left-middle' | 'left-end' |\n    'right-start' | 'right-middle' | 'right-end' |\n    'bottom-start' | 'bottom-middle' | 'bottom-end' | Direction;\n\nexport type NanoPopOptions = {\n    container: DOMRect;\n    position: NanoPopPosition;\n    variantFlipOrder: VariantFlipOrder;\n    positionFlipOrder: PositionFlipOrder;\n    margin: number;\n    reference?: HTMLElement;\n    popper?: HTMLElement;\n};\n\ntype AvailablePositions = {\n    t: number;\n    b: number;\n    l: number;\n    r: number;\n};\n\ntype AvailableVariants = {\n    vs: number;\n    vm: number;\n    ve: number;\n    hs: number;\n    hm: number;\n    he: number;\n};\n\ntype PositionPairs = [Direction, Direction];\nexport type PositionMatch = 'ts' | 'tm' | 'te' | 'bs' | 'bm' | 'be' | 'ls' | 'lm' | 'le' | 'rs' | 'rm' | 're';\n\nexport interface NanoPop {\n    update(updatedOptions?: Partial<NanoPopOptions>): PositionMatch | null;\n}\n\nexport interface NanoPopConstructor {\n\n    /**\n     * @param reference Reference element\n     * @param popper Actual popper element\n     * @param options Optional options\n     */\n    (reference: HTMLElement, popper: HTMLElement, options?: Partial<NanoPopOptions>): NanoPop;\n\n    /**\n     * @param options Partial options which get merged with the current one\n     */\n    (options?: Partial<NanoPopOptions>): NanoPop;\n}\n\n// Export current version\nexport const version = VERSION;\n\n// Export default\nexport const defaults = {\n    variantFlipOrder: {start: 'sme', middle: 'mse', end: 'ems'},\n    positionFlipOrder: {top: 'tbrl', right: 'rltb', bottom: 'btrl', left: 'lrbt'},\n    position: 'bottom',\n    margin: 8\n};\n\n/**\n * Repositions an element once using the provided options and elements.\n * @param reference Reference element\n * @param popper Popper element\n * @param opt Optional, additional options\n */\nexport const reposition = (\n    reference: HTMLElement,\n    popper: HTMLElement,\n    opt?: Partial<NanoPopOptions>\n): PositionMatch | null => {\n    const {\n        container,\n        margin,\n        position,\n        variantFlipOrder,\n        positionFlipOrder\n    } = {\n        container: document.documentElement.getBoundingClientRect(),\n        ...defaults,\n        ...opt\n    };\n\n    /**\n     * Reset position to resolve viewport\n     * See https://developer.mozilla.org/en-US/docs/Web/CSS/position#fixed\n     */\n    const {left: originalLeft, top: originalTop} = popper.style;\n    popper.style.left = '0';\n    popper.style.top = '0';\n\n    const refBox = reference.getBoundingClientRect();\n    const popBox = popper.getBoundingClientRect();\n\n    /**\n     * Holds coordinates of top, left, bottom and right alignment\n     */\n    const positionStore: AvailablePositions = {\n        t: refBox.top - popBox.height - margin,\n        b: refBox.bottom + margin,\n        r: refBox.right + margin,\n        l: refBox.left - popBox.width - margin\n    };\n\n    /**\n     * Holds corresponding variants (start, middle, end).\n     * The values depend on horizontal / vertical orientation\n     */\n    const variantStore: AvailableVariants = {\n        vs: refBox.left,\n        vm: refBox.left + refBox.width / 2 + -popBox.width / 2,\n        ve: refBox.left + refBox.width - popBox.width,\n        hs: refBox.top,\n        hm: refBox.bottom - refBox.height / 2 - popBox.height / 2,\n        he: refBox.bottom - popBox.height\n    };\n\n    // Extract position and variant\n    // Top-start -> top is \"position\" and \"start\" is the variant\n    const [posKey, varKey = 'middle'] = position.split('-');\n    const positions = positionFlipOrder[posKey as keyof PositionFlipOrder];\n    const variants = variantFlipOrder[varKey as keyof VariantFlipOrder];\n\n    // Try out all possible combinations, starting with the preferred one.\n    const {top, left, bottom, right} = container;\n\n    for (const p of positions) {\n        const vertical = (p === 't' || p === 'b');\n\n        // The position-value\n        const positionVal = positionStore[p as keyof AvailablePositions];\n\n        // Which property has to be changes.\n        const [positionKey, variantKey] = (vertical ? ['top', 'left'] : ['left', 'top']) as PositionPairs;\n\n        /**\n         * box refers to the size of the popper element. Depending on the orientation this is width or height.\n         * The limit is the corresponding, maximum value for this position.\n         */\n        const [positionSize, variantSize] = vertical ? [popBox.height, popBox.width] : [popBox.width, popBox.height];\n        const [positionMaximum, variantMaximum] = vertical ? [bottom, right] : [right, bottom];\n        const [positionMinimum, variantMinimum] = vertical ? [top, left] : [left, top];\n\n        // Skip pre-clipped values\n        if (positionVal < positionMinimum || (positionVal + positionSize) > positionMaximum) {\n            continue;\n        }\n\n        for (const v of variants) {\n\n            // The position-value, the related size value of the popper and the limit\n            const variantVal = variantStore[((vertical ? 'v' : 'h') + v) as keyof AvailableVariants];\n\n            if (variantVal < variantMinimum || (variantVal + variantSize) > variantMaximum) {\n                continue;\n            }\n\n            // Apply styles and normalize viewport\n            popper.style[variantKey] = `${variantVal - popBox[variantKey]}px`;\n            popper.style[positionKey] = `${positionVal - popBox[positionKey]}px`;\n            return (p + v) as PositionMatch;\n        }\n    }\n\n    // Revert style values (won't work with styled-elements or similar systems)\n    // \"Fix\" for https://github.com/Simonwep/nanopop/issues/7\n    popper.style.left = originalLeft;\n    popper.style.top = originalTop;\n\n    return null;\n};\n\n/**\n * Creates a stateful popper.\n * You can either...\n * ... pass an options object: createPopper(<options>)\n * ... pass both the reference and popper: create(<ref>, <el>, <?options>)\n * ... pass nothing, in this case you'll have to set at least both a reference and a popper in update.\n *\n * @param reference | options Reference element or options\n * @param popper Popper element\n * @param options Optional additional options\n */\nexport const createPopper: NanoPopConstructor = (\n    reference?: HTMLElement | Partial<NanoPopOptions>,\n    popper?: HTMLElement,\n    options?: Partial<NanoPopOptions>\n): NanoPop => {\n\n    // Resolve options\n    const baseOptions: Partial<NanoPopOptions> = typeof reference === 'object' && !(reference instanceof HTMLElement) ?\n        reference : {reference, popper, ...options};\n\n    return {\n\n        /**\n         * Repositions the current popper.\n         * @param options Optional options which get merged with the current ones.\n         */\n        update(options: Partial<NanoPopOptions> = baseOptions): PositionMatch | null {\n            const {reference, popper} = Object.assign(baseOptions, options);\n\n            if (!popper || !reference) {\n                throw new Error('Popper- or reference-element missing.');\n            }\n\n            return reposition(reference, popper, baseOptions);\n        }\n    };\n};\n", "import * as _ from './utils/utils';\nimport {parseToHSVA} from './utils/color';\nimport {HSVaColor} from './utils/hsvacolor';\nimport Moveable from './libs/moveable';\nimport Selectable from './libs/selectable';\nimport buildPickr from './template';\nimport {createPopper} from 'nanopop';\n\nexport default class Pickr {\n\n    // Expose pickr utils\n    static utils = _;\n\n    // Assign version and export\n    static version = VERSION;\n\n    // Default strings\n    static I18N_DEFAULTS = {\n\n        // Strings visible in the UI\n        'ui:dialog': 'color picker dialog',\n        'btn:toggle': 'toggle color picker dialog',\n        'btn:swatch': 'color swatch',\n        'btn:last-color': 'use previous color',\n        'btn:save': 'Save',\n        'btn:cancel': 'Cancel',\n        'btn:clear': 'Clear',\n\n        // Strings used for aria-labels\n        'aria:btn:save': 'save and close',\n        'aria:btn:cancel': 'cancel and close',\n        'aria:btn:clear': 'clear and close',\n        'aria:input': 'color input field',\n        'aria:palette': 'color selection area',\n        'aria:hue': 'hue selection slider',\n        'aria:opacity': 'selection slider'\n    };\n\n    // Default options\n    static DEFAULT_OPTIONS = {\n        appClass: null,\n        theme: 'classic',\n        useAsButton: false,\n        padding: 8,\n        disabled: false,\n        comparison: true,\n        closeOnScroll: false,\n        outputPrecision: 0,\n        lockOpacity: false,\n        autoReposition: true,\n        container: 'body',\n\n        components: {\n            interaction: {}\n        },\n\n        i18n: {},\n        swatches: null,\n        inline: false,\n        sliders: null,\n\n        default: '#42445a',\n        defaultRepresentation: null,\n        position: 'bottom-middle',\n        adjustableNumbers: true,\n        showAlways: false,\n\n        closeWithKey: 'Escape'\n    };\n\n    // Will be used to prevent specific actions during initilization\n    _initializingActive = true;\n\n    // If the current color value should be recalculated\n    _recalc = true;\n\n    // Positioning engine and DOM-Tree\n    _nanopop = null;\n    _root = null;\n\n    // Current and last color for comparison\n    _color = HSVaColor();\n    _lastColor = HSVaColor();\n    _swatchColors = [];\n\n    // Animation frame used for setup.\n    // Will be cancelled in case of destruction.\n    _setupAnimationFrame = null;\n\n    // Evenlistener name: [callbacks]\n    _eventListener = {\n        init: [],\n        save: [],\n        hide: [],\n        show: [],\n        clear: [],\n        change: [],\n        changestop: [],\n        cancel: [],\n        swatchselect: []\n    };\n\n    constructor(opt) {\n\n        // Assign default values\n        this.options = opt = Object.assign({...Pickr.DEFAULT_OPTIONS}, opt);\n\n        const {swatches, components, theme, sliders, lockOpacity, padding} = opt;\n\n        if (['nano', 'monolith'].includes(theme) && !sliders) {\n            opt.sliders = 'h';\n        }\n\n        // Check interaction section\n        if (!components.interaction) {\n            components.interaction = {};\n        }\n\n        // Overwrite palette if preview, opacity or hue are true\n        const {preview, opacity, hue, palette} = components;\n        components.opacity = (!lockOpacity && opacity);\n        components.palette = palette || preview || opacity || hue;\n\n        // Initialize picker\n        this._preBuild();\n        this._buildComponents();\n        this._bindEvents();\n        this._finalBuild();\n\n        // Append pre-defined swatch colors\n        if (swatches && swatches.length) {\n            swatches.forEach(color => this.addSwatch(color));\n        }\n\n        // Initialize positioning engine\n        const {button, app} = this._root;\n        this._nanopop = createPopper(button, app, {\n            margin: padding\n        });\n\n        // Initialize accessibility\n        button.setAttribute('role', 'button');\n        button.setAttribute('aria-label', this._t('btn:toggle'));\n\n        // Initilization is finish, pickr is visible and ready for usage\n        const that = this;\n        this._setupAnimationFrame = requestAnimationFrame((function cb() {\n\n            // TODO: Performance issue due to high call-rate?\n            if (!app.offsetWidth) {\n                return requestAnimationFrame(cb);\n            }\n\n            // Apply default color\n            that.setColor(opt.default);\n            that._rePositioningPicker();\n\n            // Initialize color representation\n            if (opt.defaultRepresentation) {\n                that._representation = opt.defaultRepresentation;\n                that.setColorRepresentation(that._representation);\n            }\n\n            // Show pickr if locked\n            if (opt.showAlways) {\n                that.show();\n            }\n\n            // Initialization is done - pickr is usable, fire init event\n            that._initializingActive = false;\n            that._emit('init');\n        }));\n    }\n\n    // Create instance via method\n    static create = options => new Pickr(options);\n\n    // Does only the absolutly basic thing to initialize the components\n    _preBuild() {\n        const {options} = this;\n\n        // Resolve elements\n        for (const type of ['el', 'container']) {\n            options[type] = _.resolveElement(options[type]);\n        }\n\n        // Create element and append it to body to\n        // Prevent initialization errors\n        this._root = buildPickr(this);\n\n        // Check if a custom button is used\n        if (options.useAsButton) {\n            this._root.button = options.el; // Replace button with customized button\n        }\n\n        options.container.appendChild(this._root.root);\n    }\n\n    _finalBuild() {\n        const opt = this.options;\n        const root = this._root;\n\n        // Remove from body\n        opt.container.removeChild(root.root);\n\n        if (opt.inline) {\n            const parent = opt.el.parentElement;\n\n            if (opt.el.nextSibling) {\n                parent.insertBefore(root.app, opt.el.nextSibling);\n            } else {\n                parent.appendChild(root.app);\n            }\n        } else {\n            opt.container.appendChild(root.app);\n        }\n\n        // Don't replace the the element if a custom button is used\n        if (!opt.useAsButton) {\n\n            // Replace element with actual color-picker\n            opt.el.parentNode.replaceChild(root.root, opt.el);\n        } else if (opt.inline) {\n            opt.el.remove();\n        }\n\n        // Check if it should be immediatly disabled\n        if (opt.disabled) {\n            this.disable();\n        }\n\n        // Check if color comparison is disabled, if yes - remove transitions so everything keeps smoothly\n        if (!opt.comparison) {\n            root.button.style.transition = 'none';\n\n            if (!opt.useAsButton) {\n                root.preview.lastColor.style.transition = 'none';\n            }\n        }\n\n        this.hide();\n    }\n\n    _buildComponents() {\n\n        // Instance reference\n        const inst = this;\n        const cs = this.options.components;\n        const sliders = (inst.options.sliders || 'v').repeat(2);\n        const [so, sh] = sliders.match(/^[vh]+$/g) ? sliders : [];\n\n        // Re-assign if null\n        const getColor = () =>\n            this._color || (this._color = this._lastColor.clone());\n\n        const components = {\n\n            palette: Moveable({\n                element: inst._root.palette.picker,\n                wrapper: inst._root.palette.palette,\n\n                onstop: () => inst._emit('changestop', 'slider', inst),\n                onchange(x, y) {\n                    if (!cs.palette) {\n                        return;\n                    }\n\n                    const color = getColor();\n                    const {_root, options} = inst;\n                    const {lastColor, currentColor} = _root.preview;\n\n                    // Update the input field only if the user is currently not typing\n                    if (inst._recalc) {\n\n                        // Calculate saturation based on the position\n                        color.s = x * 100;\n\n                        // Calculate the value\n                        color.v = 100 - y * 100;\n\n                        // Prevent falling under zero\n                        color.v < 0 ? color.v = 0 : 0;\n                        inst._updateOutput('slider');\n                    }\n\n                    // Set picker and gradient color\n                    const cssRGBaString = color.toRGBA().toString(0);\n                    this.element.style.background = cssRGBaString;\n                    this.wrapper.style.background = `\n                        linear-gradient(to top, rgba(0, 0, 0, ${color.a}), transparent),\n                        linear-gradient(to left, hsla(${color.h}, 100%, 50%, ${color.a}), rgba(255, 255, 255, ${color.a}))\n                    `;\n\n                    // Check if color is locked\n                    if (!options.comparison) {\n                        _root.button.style.setProperty('--pcr-color', cssRGBaString);\n\n                        // If the user changes the color, remove the cleared icon\n                        _root.button.classList.remove('clear');\n                    } else if (!options.useAsButton && !inst._lastColor) {\n\n                        // Apply color to both the last and current color since the current state is cleared\n                        lastColor.style.setProperty('--pcr-color', cssRGBaString);\n                    }\n\n                    // Check if there's a swatch which color matches the current one\n                    const hexa = color.toHEXA().toString();\n                    for (const {el, color} of inst._swatchColors) {\n                        el.classList[hexa === color.toHEXA().toString() ? 'add' : 'remove']('pcr-active');\n                    }\n\n                    // Change current color\n                    currentColor.style.setProperty('--pcr-color', cssRGBaString);\n                }\n            }),\n\n            hue: Moveable({\n                lock: sh === 'v' ? 'h' : 'v',\n                element: inst._root.hue.picker,\n                wrapper: inst._root.hue.slider,\n\n                onstop: () => inst._emit('changestop', 'slider', inst),\n                onchange(v) {\n                    if (!cs.hue || !cs.palette) {\n                        return;\n                    }\n\n                    const color = getColor();\n\n                    // Calculate hue\n                    if (inst._recalc) {\n                        color.h = v * 360;\n                    }\n\n                    // Update color\n                    this.element.style.backgroundColor = `hsl(${color.h}, 100%, 50%)`;\n                    components.palette.trigger();\n                }\n            }),\n\n            opacity: Moveable({\n                lock: so === 'v' ? 'h' : 'v',\n                element: inst._root.opacity.picker,\n                wrapper: inst._root.opacity.slider,\n\n                onstop: () => inst._emit('changestop', 'slider', inst),\n                onchange(v) {\n                    if (!cs.opacity || !cs.palette) {\n                        return;\n                    }\n\n                    const color = getColor();\n\n                    // Calculate opacity\n                    if (inst._recalc) {\n                        color.a = Math.round(v * 1e2) / 100;\n                    }\n\n                    // Update color\n                    this.element.style.background = `rgba(0, 0, 0, ${color.a})`;\n                    components.palette.trigger();\n                }\n            }),\n\n            selectable: Selectable({\n                elements: inst._root.interaction.options,\n                className: 'active',\n\n                onchange(e) {\n                    inst._representation = e.target.getAttribute('data-type').toUpperCase();\n                    inst._recalc && inst._updateOutput('swatch');\n                }\n            })\n        };\n\n        this._components = components;\n    }\n\n    _bindEvents() {\n        const {_root, options} = this;\n\n        const eventBindings = [\n\n            // Clear color\n            _.on(_root.interaction.clear, 'click', () => this._clearColor()),\n\n            // Select last color on click\n            _.on([\n                _root.interaction.cancel,\n                _root.preview.lastColor\n            ], 'click', () => {\n                this.setHSVA(...(this._lastColor || this._color).toHSVA(), true);\n                this._emit('cancel');\n            }),\n\n            // Save color\n            _.on(_root.interaction.save, 'click', () => {\n                !this.applyColor() && !options.showAlways && this.hide();\n            }),\n\n            // User input\n            _.on(_root.interaction.result, ['keyup', 'input'], e => {\n\n                // Fire listener if initialization is finish and changed color was valid\n                if (this.setColor(e.target.value, true) && !this._initializingActive) {\n                    this._emit('change', this._color, 'input', this);\n                    this._emit('changestop', 'input', this);\n                }\n\n                e.stopImmediatePropagation();\n            }),\n\n            // Detect user input and disable auto-recalculation\n            _.on(_root.interaction.result, ['focus', 'blur'], e => {\n                this._recalc = e.type === 'blur';\n                this._recalc && this._updateOutput(null);\n            }),\n\n            // Cancel input detection on color change\n            _.on([\n                _root.palette.palette,\n                _root.palette.picker,\n                _root.hue.slider,\n                _root.hue.picker,\n                _root.opacity.slider,\n                _root.opacity.picker\n            ], ['mousedown', 'touchstart'], () => this._recalc = true, {passive: true})\n        ];\n\n        // Provide hiding / showing abilities only if showAlways is false\n        if (!options.showAlways) {\n            const ck = options.closeWithKey;\n\n            eventBindings.push(\n\n                // Save and hide / show picker\n                _.on(_root.button, 'click', () => this.isOpen() ? this.hide() : this.show()),\n\n                // Close with escape key\n                _.on(document, 'keyup', e => this.isOpen() && (e.key === ck || e.code === ck) && this.hide()),\n\n                // Cancel selecting if the user taps behind the color picker\n                _.on(document, ['touchstart', 'mousedown'], e => {\n                    if (this.isOpen() && !_.eventPath(e).some(el => el === _root.app || el === _root.button)) {\n                        this.hide();\n                    }\n                }, {capture: true})\n            );\n        }\n\n        // Make input adjustable if enabled\n        if (options.adjustableNumbers) {\n            const ranges = {\n                rgba: [255, 255, 255, 1],\n                hsva: [360, 100, 100, 1],\n                hsla: [360, 100, 100, 1],\n                cmyk: [100, 100, 100, 100]\n            };\n\n            _.adjustableInputNumbers(_root.interaction.result, (o, step, index) => {\n                const range = ranges[this.getColorRepresentation().toLowerCase()];\n\n                if (range) {\n                    const max = range[index];\n\n                    // Calculate next reasonable number\n                    const nv = o + (max >= 100 ? step * 1000 : step);\n\n                    // Apply range of zero up to max, fix floating-point issues\n                    return nv <= 0 ? 0 : Number((nv < max ? nv : max).toPrecision(3));\n                }\n\n                return o;\n            });\n        }\n\n        if (options.autoReposition && !options.inline) {\n            let timeout = null;\n            const that = this;\n\n            // Re-calc position on window resize, scroll and wheel\n            eventBindings.push(\n                _.on(window, ['scroll', 'resize'], () => {\n                    if (that.isOpen()) {\n\n                        if (options.closeOnScroll) {\n                            that.hide();\n                        }\n\n                        if (timeout === null) {\n                            timeout = setTimeout(() => timeout = null, 100);\n\n                            // Update position on every frame\n                            requestAnimationFrame(function rs() {\n                                that._rePositioningPicker();\n                                (timeout !== null) && requestAnimationFrame(rs);\n                            });\n                        } else {\n                            clearTimeout(timeout);\n                            timeout = setTimeout(() => timeout = null, 100);\n                        }\n                    }\n                }, {capture: true})\n            );\n        }\n\n        // Save bindings\n        this._eventBindings = eventBindings;\n    }\n\n    _rePositioningPicker() {\n        const {options} = this;\n\n        // No repositioning needed if inline\n        if (!options.inline) {\n            const success = this._nanopop.update({\n                container: document.body.getBoundingClientRect(),\n                position: options.position\n            });\n\n            if (!success) {\n                const el = this._root.app;\n                const eb = el.getBoundingClientRect();\n                el.style.top = `${(window.innerHeight - eb.height) / 2}px`;\n                el.style.left = `${(window.innerWidth - eb.width) / 2}px`;\n            }\n        }\n    }\n\n    _updateOutput(eventSource) {\n        const {_root, _color, options} = this;\n\n        // Check if component is present\n        if (_root.interaction.type()) {\n\n            // Construct function name and call if present\n            const method = `to${_root.interaction.type().getAttribute('data-type')}`;\n            _root.interaction.result.value = typeof _color[method] === 'function' ?\n                _color[method]().toString(options.outputPrecision) : '';\n        }\n\n        // Fire listener if initialization is finish\n        if (!this._initializingActive && this._recalc) {\n            this._emit('change', _color, eventSource, this);\n        }\n    }\n\n    _clearColor(silent = false) {\n        const {_root, options} = this;\n\n        // Change only the button color if it isn't customized\n        if (!options.useAsButton) {\n            _root.button.style.setProperty('--pcr-color', 'rgba(0, 0, 0, 0.15)');\n        }\n\n        _root.button.classList.add('clear');\n\n        if (!options.showAlways) {\n            this.hide();\n        }\n\n        this._lastColor = null;\n        if (!this._initializingActive && !silent) {\n\n            // Fire listener\n            this._emit('save', null);\n            this._emit('clear');\n        }\n    }\n\n    _parseLocalColor(str) {\n        const {values, type, a} = parseToHSVA(str);\n        const {lockOpacity} = this.options;\n        const alphaMakesAChange = a !== undefined && a !== 1;\n\n        // If no opacity is applied, add undefined at the very end which gets\n        // Set to 1 in setHSVA\n        if (values && values.length === 3) {\n            values[3] = undefined;\n        }\n\n        return {\n            values: (!values || (lockOpacity && alphaMakesAChange)) ? null : values,\n            type\n        };\n    }\n\n    _t(key) {\n        return this.options.i18n[key] || Pickr.I18N_DEFAULTS[key];\n    }\n\n    _emit(event, ...args) {\n        this._eventListener[event].forEach(cb => cb(...args, this));\n    }\n\n    on(event, cb) {\n        this._eventListener[event].push(cb);\n        return this;\n    }\n\n    off(event, cb) {\n        const callBacks = (this._eventListener[event] || []);\n        const index = callBacks.indexOf(cb);\n\n        if (~index) {\n            callBacks.splice(index, 1);\n        }\n\n        return this;\n    }\n\n    /**\n     * Appends a color to the swatch palette\n     * @param color\n     * @returns {boolean}\n     */\n    addSwatch(color) {\n        const {values} = this._parseLocalColor(color);\n\n        if (values) {\n            const {_swatchColors, _root} = this;\n            const color = HSVaColor(...values);\n\n            // Create new swatch HTMLElement\n            const el = _.createElementFromString(\n                `<button type=\"button\" style=\"--pcr-color: ${color.toRGBA().toString(0)}\" aria-label=\"${this._t('btn:swatch')}\"/>`\n            );\n\n            // Append element and save swatch data\n            _root.swatches.appendChild(el);\n            _swatchColors.push({el, color});\n\n            // Bind event\n            this._eventBindings.push(\n                _.on(el, 'click', () => {\n                    this.setHSVA(...color.toHSVA(), true);\n                    this._emit('swatchselect', color);\n                    this._emit('change', color, 'swatch', this);\n                })\n            );\n\n            return true;\n        }\n\n        return false;\n    }\n\n    /**\n     * Removes a swatch color by it's index\n     * @param index\n     * @returns {boolean}\n     */\n    removeSwatch(index) {\n        const swatchColor = this._swatchColors[index];\n\n        // Check swatch data\n        if (swatchColor) {\n            const {el} = swatchColor;\n\n            // Remove HTML child and swatch data\n            this._root.swatches.removeChild(el);\n            this._swatchColors.splice(index, 1);\n            return true;\n        }\n\n        return false;\n    }\n\n    applyColor(silent = false) {\n        const {preview, button} = this._root;\n\n        // Change preview and current color\n        const cssRGBaString = this._color.toRGBA().toString(0);\n        preview.lastColor.style.setProperty('--pcr-color', cssRGBaString);\n\n        // Change only the button color if it isn't customized\n        if (!this.options.useAsButton) {\n            button.style.setProperty('--pcr-color', cssRGBaString);\n        }\n\n        // User changed the color so remove the clear clas\n        button.classList.remove('clear');\n\n        // Save last color\n        this._lastColor = this._color.clone();\n\n        // Fire listener\n        if (!this._initializingActive && !silent) {\n            this._emit('save', this._color);\n        }\n\n        return this;\n    }\n\n    /**\n     * Destroy's all functionalitys\n     */\n    destroy() {\n\n        // Cancel setup-frame if set\n        cancelAnimationFrame(this._setupAnimationFrame);\n\n        // Unbind events\n        this._eventBindings.forEach(args => _.off(...args));\n\n        // Destroy sub-components\n        Object.keys(this._components)\n            .forEach(key => this._components[key].destroy());\n    }\n\n    /**\n     * Destroy's all functionalitys and removes\n     * the pickr element.\n     */\n    destroyAndRemove() {\n        this.destroy();\n        const {root, app} = this._root;\n\n        // Remove element\n        if (root.parentElement) {\n            root.parentElement.removeChild(root);\n        }\n\n        // Remove .pcr-app\n        app.parentElement.removeChild(app);\n\n        // There are references to various DOM elements stored in the pickr instance\n        // This cleans all of them to avoid detached DOMs\n        Object.keys(this)\n            .forEach(key => this[key] = null);\n    }\n\n    /**\n     * Hides the color-picker ui.\n     */\n    hide() {\n        if (this.isOpen()) {\n            this._root.app.classList.remove('visible');\n            this._emit('hide');\n            return true;\n        }\n\n        return false;\n    }\n\n    /**\n     * Shows the color-picker ui.\n     */\n    show() {\n        if (!this.options.disabled && !this.isOpen()) {\n            this._root.app.classList.add('visible');\n            this._rePositioningPicker();\n            this._emit('show', this._color);\n            return this;\n        }\n\n        return false;\n    }\n\n    /**\n     * @return {boolean} If the color picker is currently open\n     */\n    isOpen() {\n        return this._root.app.classList.contains('visible');\n    }\n\n    /**\n     * Set a specific color.\n     * @param h Hue\n     * @param s Saturation\n     * @param v Value\n     * @param a Alpha channel (0 - 1)\n     * @param silent If the button should not change the color\n     * @return boolean if the color has been accepted\n     */\n    setHSVA(h = 360, s = 0, v = 0, a = 1, silent = false) {\n\n        // Deactivate color calculation\n        const recalc = this._recalc; // Save state\n        this._recalc = false;\n\n        // Validate input\n        if (h < 0 || h > 360 || s < 0 || s > 100 || v < 0 || v > 100 || a < 0 || a > 1) {\n            return false;\n        }\n\n        // Override current color and re-active color calculation\n        this._color = HSVaColor(h, s, v, a);\n\n        // Update slider and palette\n        const {hue, opacity, palette} = this._components;\n        hue.update((h / 360));\n        opacity.update(a);\n        palette.update(s / 100, 1 - (v / 100));\n\n        // Check if call is silent\n        if (!silent) {\n            this.applyColor();\n        }\n\n        // Update output if recalculation is enabled\n        if (recalc) {\n            this._updateOutput();\n        }\n\n        // Restore old state\n        this._recalc = recalc;\n        return true;\n    }\n\n    /**\n     * Tries to parse a string which represents a color.\n     * Examples: #fff\n     *           rgb 10 10 200\n     *           hsva 10 20 5 0.5\n     * @param string\n     * @param silent\n     */\n    setColor(string, silent = false) {\n\n        // Check if null\n        if (string === null) {\n            this._clearColor(silent);\n            return true;\n        }\n\n        const {values, type} = this._parseLocalColor(string);\n\n        // Check if color is ok\n        if (values) {\n\n            // Change selected color format\n            const utype = type.toUpperCase();\n            const {options} = this._root.interaction;\n            const target = options.find(el => el.getAttribute('data-type') === utype);\n\n            // Auto select only if not hidden\n            if (target && !target.hidden) {\n                for (const el of options) {\n                    el.classList[el === target ? 'add' : 'remove']('active');\n                }\n            }\n\n            // Update color (fires 'save' event if silent is 'false')\n            if (!this.setHSVA(...values, silent)) {\n                return false;\n            }\n\n            // Update representation (fires 'change' event)\n            return this.setColorRepresentation(utype);\n        }\n\n        return false;\n    }\n\n    /**\n     * Changes the color _representation.\n     * Allowed values are HEX, RGB, HSV, HSL and CMYK\n     * @param type\n     * @returns {boolean} if the selected type was valid.\n     */\n    setColorRepresentation(type) {\n\n        // Force uppercase to allow a case-sensitiv comparison\n        type = type.toUpperCase();\n\n        // Find button with given type and trigger click event\n        return !!this._root.interaction.options\n            .find(v => v.getAttribute('data-type').startsWith(type) && !v.click());\n    }\n\n    /**\n     * Returns the current color representaion. See setColorRepresentation\n     * @returns {*}\n     */\n    getColorRepresentation() {\n        return this._representation;\n    }\n\n    /**\n     * @returns HSVaColor Current HSVaColor object.\n     */\n    getColor() {\n        return this._color;\n    }\n\n    /**\n     * Returns the currently selected color.\n     * @returns {{a, toHSVA, toHEXA, s, v, h, clone, toCMYK, toHSLA, toRGBA}}\n     */\n    getSelectedColor() {\n        return this._lastColor;\n    }\n\n    /**\n     * @returns The root HTMLElement with all his components.\n     */\n    getRoot() {\n        return this._root;\n    }\n\n    /**\n     * Disable pickr\n     */\n    disable() {\n        this.hide();\n        this.options.disabled = true;\n        this._root.button.classList.add('disabled');\n        return this;\n    }\n\n    /**\n     * Enable pickr\n     */\n    enable() {\n        this.options.disabled = false;\n        this._root.button.classList.remove('disabled');\n        return this;\n    }\n}\n"], "sourceRoot": ""}