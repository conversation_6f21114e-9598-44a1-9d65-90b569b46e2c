<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=5.0, minimum-scale=1.0"/>
    <meta http-equiv="X-UA-Compatible" content="ie=edge"/>

    <!-- Chrome, Firefox OS and Opera -->
    <meta name="theme-color" content="#42445A"/>

    <!-- Windows Phone -->
    <meta name="msapplication-navbutton-color" content="#42445A"/>

    <!-- iOS Safari -->
    <meta name="apple-mobile-web-app-status-bar-style" content="#42445A"/>

    <!-- SEO <PERSON> -->
    <meta name="audience" lang="en" content="all"/>
    <meta name="expires" content="7 days"/>
    <meta name="robots" content="follow"/>
    <meta name="revisit-after" content="7 days"/>
    <meta name="page-topic" content="Color picker"/>
    <meta name="copyright" content="<PERSON>"/>
    <meta name="author" content="<PERSON>"/>
    <meta name="description"
          content="Flat, simple, responsive and hackable Color-Picker library. No dependencies, no jQuery. Compatible with all CSS Frameworks e.g. Bootstrap, Materialize. Supports alpha channel, rgba, hsla, hsva and more!"/>
    <meta name="keywords" content="colorpicker, color-picker, design, ux-design, ui-design, web-development, programming, colors"/>

    <!-- Open Graph / Facebook -->
    <meta property="og:image" content="st/favicon.png"/>
    <meta property="og:type" content="website"/>
    <meta property="og:url" content="https://simonwep.github.io/pickr/"/>
    <meta property="og:site_name" content="Pickr"/>
    <meta property="og:title" content="Pickr - Modern Color Picker"/>
    <meta property="og:description"
          content="Flat, simple, responsive and hackable Color-Picker. No dependencies, no jQuery. Compatible with all CSS Frameworks e.g. Bootstrap, Materialize."/>

    <!-- Twitter -->
    <meta property="twitter:url" content="https://simonwep.github.io/pickr/">
    <meta property="twitter:title" content="Pickr - Modern Color Picker">
    <meta property="twitter:description"
          content="Flat, simple, responsive and hackable Color-Picker library. No dependencies, no jQuery. Compatible with all CSS Frameworks e.g. Bootstrap, Materialize. Supports alpha channel, rgba, hsla, hsva and more!">
    <meta property="twitter:image" content="st/favicon.png">

    <!-- Analytics -->
    <script async src="https://ackee.reinisch.io/tracker.js" data-ackee-server="https://ackee.reinisch.io" data-ackee-domain-id="6536a4cf-bb51-4901-ad17-707906e53ff8"></script>

    <title>Pickr</title>

    <!-- Icons -->
    <link rel="icon" href="www/favicon.png"/>
    <link rel="fluid-icon" href="www/favicon.png"/>
    <link rel="apple-touch-icon" href="www/favicon.png"/>

    <!-- Style sheets -->
    <link href="https://fonts.googleapis.com/css?family=Montserrat:300,400&display=swap" rel="stylesheet">
    <link href="dist/themes/classic.min.css" rel="stylesheet"/>
    <link href="dist/themes/monolith.min.css" rel="stylesheet"/>
    <link href="dist/themes/nano.min.css" rel="stylesheet"/>
    <link href="www/index.css" rel="stylesheet"/>
</head>

<body>
<header>
    <h1>Pickr. Keep it simple.</h1>
    <a href="https://github.com/Simonwep/pickr">VIEW ON GITHUB</a>
</header>

<main>
    <div class="theme-container"></div>
    <div class="pickr-container"></div>
    <p>(Tap it)</p>
</main>

<script src="dist/pickr.es5.min.js"></script>
<script src="www/index.js"></script>
</body>
</html>
