{"name": "compute-scroll-into-view", "version": "1.0.20", "description": "The engine that powers scroll-into-view-if-needed", "keywords": ["if-needed", "scroll", "scroll-into-view", "scroll-into-view-if-needed", "scrollIntoView", "scrollIntoViewIfNeeded", "scrollMode", "typescript"], "homepage": "https://scroll-into-view.dev", "repository": {"type": "git", "url": "git+https://github.com/scroll-into-view/compute-scroll-into-view.git"}, "license": "MIT", "author": "<PERSON>", "sideEffects": false, "exports": {".": {"types": "./typings/index.d.ts", "source": "./src/index.ts", "import": "./dist/index.mjs", "require": "./dist/index.js", "default": "./dist/index.mjs"}, "./package.json": "./package.json"}, "main": "dist/index.js", "umd:main": "umd/compute-scroll-into-view.min.js", "module": "dist/index.mjs", "source": "src/index.ts", "typings": "typings/index.d.ts", "files": ["dist", "typings", "umd/compute-scroll-into-view.min.js", "umd/compute-scroll-into-view.min.js.map"], "scripts": {"prebuild": "<PERSON><PERSON>f 'dist' 'umd'", "build": "npm run build:dist && npm run build:umd", "build:dist": "microbundle -f cjs,es", "build:umd": "microbundle -f umd -o umd", "prepublishOnly": "npm run build", "test": "jest -c integration/jest.config.js", "typecheck": "tsc --noEmit"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts,tsx,json,md}": "prettier --write"}, "browserify": {"transform": ["loose-envify"]}, "prettier": {"semi": false, "singleQuote": true}, "devDependencies": {"@sanity/semantic-release-preset": "^2.0.2", "husky": "^8.0.2", "jest": "^29.3.1", "jest-junit": "^15.0.0", "jest-puppeteer": "^6.1.1", "lint-staged": "^13.0.4", "microbundle": "^0.15.1", "prettier": "^2.8.0", "prettier-plugin-packagejson": "^2.3.0", "puppeteer": "^19.3.0", "rimraf": "^3.0.2", "serve": "^14.1.2", "typescript": "^4.9.3"}, "bundlesize": [{"path": "./umd/compute-scroll-into-view.min.js", "maxSize": "3 kB", "compression": "none"}]}