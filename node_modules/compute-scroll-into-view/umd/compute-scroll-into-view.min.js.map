{"version": 3, "file": "compute-scroll-into-view.min.js", "sources": ["../src/index.ts"], "sourcesContent": ["// Compute what scrolling needs to be done on required scrolling boxes for target to be in view\n\n// The type names here are named after the spec to make it easier to find more information around what they mean:\n// To reduce churn and reduce things that need be maintained things from the official TS DOM library is used here\n// https://drafts.csswg.org/cssom-view/\n\n// For a definition on what is \"block flow direction\" exactly, check this: https://drafts.csswg.org/css-writing-modes-4/#block-flow-direction\n\n// add support for visualViewport object currently implemented in chrome\ninterface visualViewport {\n  height: number\n  width: number\n}\n\ntype ScrollLogicalPosition = 'start' | 'center' | 'end' | 'nearest'\n// This new option is tracked in this PR, which is the most likely candidate at the time: https://github.com/w3c/csswg-drafts/pull/1805\ntype ScrollMode = 'always' | 'if-needed'\n// New option that skips auto-scrolling all nodes with overflow: hidden set\n// See FF implementation: https://hg.mozilla.org/integration/fx-team/rev/c48c3ec05012#l7.18\ntype SkipOverflowHiddenElements = boolean\n\ninterface Options {\n  block?: ScrollLogicalPosition\n  inline?: ScrollLogicalPosition\n  scrollMode?: ScrollMode\n  boundary?: CustomScrollBoundary\n  skipOverflowHiddenElements?: SkipOverflowHiddenElements\n}\n\n// Custom behavior, not in any spec\ntype CustomScrollBoundaryCallback = (parent: Element) => boolean\ntype CustomScrollBoundary = Element | CustomScrollBoundaryCallback | null\ninterface CustomScrollAction {\n  el: Element\n  top: number\n  left: number\n}\n\n// @TODO better shadowdom test, 11 = document fragment\nfunction isElement(el: any): el is Element {\n  return typeof el === 'object' && el != null && el.nodeType === 1\n}\n\nfunction canOverflow(\n  overflow: string | null,\n  skipOverflowHiddenElements?: boolean\n) {\n  if (skipOverflowHiddenElements && overflow === 'hidden') {\n    return false\n  }\n\n  return overflow !== 'visible' && overflow !== 'clip'\n}\n\nfunction getFrameElement(el: Element) {\n  if (!el.ownerDocument || !el.ownerDocument.defaultView) {\n    return null\n  }\n\n  try {\n    return el.ownerDocument.defaultView.frameElement\n  } catch (e) {\n    return null\n  }\n}\n\nfunction isHiddenByFrame(el: Element): boolean {\n  const frame = getFrameElement(el)\n  if (!frame) {\n    return false\n  }\n\n  return (\n    frame.clientHeight < el.scrollHeight || frame.clientWidth < el.scrollWidth\n  )\n}\n\nfunction isScrollable(el: Element, skipOverflowHiddenElements?: boolean) {\n  if (el.clientHeight < el.scrollHeight || el.clientWidth < el.scrollWidth) {\n    const style = getComputedStyle(el, null)\n    return (\n      canOverflow(style.overflowY, skipOverflowHiddenElements) ||\n      canOverflow(style.overflowX, skipOverflowHiddenElements) ||\n      isHiddenByFrame(el)\n    )\n  }\n\n  return false\n}\n/**\n * Find out which edge to align against when logical scroll position is \"nearest\"\n * Interesting fact: \"nearest\" works similarily to \"if-needed\", if the element is fully visible it will not scroll it\n *\n * Legends:\n * ┌────────┐ ┏ ━ ━ ━ ┓\n * │ target │   frame\n * └────────┘ ┗ ━ ━ ━ ┛\n */\nfunction alignNearest(\n  scrollingEdgeStart: number,\n  scrollingEdgeEnd: number,\n  scrollingSize: number,\n  scrollingBorderStart: number,\n  scrollingBorderEnd: number,\n  elementEdgeStart: number,\n  elementEdgeEnd: number,\n  elementSize: number\n) {\n  /**\n   * If element edge A and element edge B are both outside scrolling box edge A and scrolling box edge B\n   *\n   *          ┌──┐\n   *        ┏━│━━│━┓\n   *          │  │\n   *        ┃ │  │ ┃        do nothing\n   *          │  │\n   *        ┗━│━━│━┛\n   *          └──┘\n   *\n   *  If element edge C and element edge D are both outside scrolling box edge C and scrolling box edge D\n   *\n   *    ┏ ━ ━ ━ ━ ┓\n   *   ┌───────────┐\n   *   │┃         ┃│        do nothing\n   *   └───────────┘\n   *    ┗ ━ ━ ━ ━ ┛\n   */\n  if (\n    (elementEdgeStart < scrollingEdgeStart &&\n      elementEdgeEnd > scrollingEdgeEnd) ||\n    (elementEdgeStart > scrollingEdgeStart && elementEdgeEnd < scrollingEdgeEnd)\n  ) {\n    return 0\n  }\n\n  /**\n   * If element edge A is outside scrolling box edge A and element height is less than scrolling box height\n   *\n   *          ┌──┐\n   *        ┏━│━━│━┓         ┏━┌━━┐━┓\n   *          └──┘             │  │\n   *  from  ┃      ┃     to  ┃ └──┘ ┃\n   *\n   *        ┗━ ━━ ━┛         ┗━ ━━ ━┛\n   *\n   * If element edge B is outside scrolling box edge B and element height is greater than scrolling box height\n   *\n   *        ┏━ ━━ ━┓         ┏━┌━━┐━┓\n   *                           │  │\n   *  from  ┃ ┌──┐ ┃     to  ┃ │  │ ┃\n   *          │  │             │  │\n   *        ┗━│━━│━┛         ┗━│━━│━┛\n   *          │  │             └──┘\n   *          │  │\n   *          └──┘\n   *\n   * If element edge C is outside scrolling box edge C and element width is less than scrolling box width\n   *\n   *       from                 to\n   *    ┏ ━ ━ ━ ━ ┓         ┏ ━ ━ ━ ━ ┓\n   *  ┌───┐                 ┌───┐\n   *  │ ┃ │       ┃         ┃   │     ┃\n   *  └───┘                 └───┘\n   *    ┗ ━ ━ ━ ━ ┛         ┗ ━ ━ ━ ━ ┛\n   *\n   * If element edge D is outside scrolling box edge D and element width is greater than scrolling box width\n   *\n   *       from                 to\n   *    ┏ ━ ━ ━ ━ ┓         ┏ ━ ━ ━ ━ ┓\n   *        ┌───────────┐   ┌───────────┐\n   *    ┃   │     ┃     │   ┃         ┃ │\n   *        └───────────┘   └───────────┘\n   *    ┗ ━ ━ ━ ━ ┛         ┗ ━ ━ ━ ━ ┛\n   */\n  if (\n    (elementEdgeStart <= scrollingEdgeStart && elementSize <= scrollingSize) ||\n    (elementEdgeEnd >= scrollingEdgeEnd && elementSize >= scrollingSize)\n  ) {\n    return elementEdgeStart - scrollingEdgeStart - scrollingBorderStart\n  }\n\n  /**\n   * If element edge B is outside scrolling box edge B and element height is less than scrolling box height\n   *\n   *        ┏━ ━━ ━┓         ┏━ ━━ ━┓\n   *\n   *  from  ┃      ┃     to  ┃ ┌──┐ ┃\n   *          ┌──┐             │  │\n   *        ┗━│━━│━┛         ┗━└━━┘━┛\n   *          └──┘\n   *\n   * If element edge A is outside scrolling box edge A and element height is greater than scrolling box height\n   *\n   *          ┌──┐\n   *          │  │\n   *          │  │             ┌──┐\n   *        ┏━│━━│━┓         ┏━│━━│━┓\n   *          │  │             │  │\n   *  from  ┃ └──┘ ┃     to  ┃ │  │ ┃\n   *                           │  │\n   *        ┗━ ━━ ━┛         ┗━└━━┘━┛\n   *\n   * If element edge C is outside scrolling box edge C and element width is greater than scrolling box width\n   *\n   *           from                 to\n   *        ┏ ━ ━ ━ ━ ┓         ┏ ━ ━ ━ ━ ┓\n   *  ┌───────────┐           ┌───────────┐\n   *  │     ┃     │   ┃       │ ┃         ┃\n   *  └───────────┘           └───────────┘\n   *        ┗ ━ ━ ━ ━ ┛         ┗ ━ ━ ━ ━ ┛\n   *\n   * If element edge D is outside scrolling box edge D and element width is less than scrolling box width\n   *\n   *           from                 to\n   *        ┏ ━ ━ ━ ━ ┓         ┏ ━ ━ ━ ━ ┓\n   *                ┌───┐             ┌───┐\n   *        ┃       │ ┃ │       ┃     │   ┃\n   *                └───┘             └───┘\n   *        ┗ ━ ━ ━ ━ ┛         ┗ ━ ━ ━ ━ ┛\n   *\n   */\n  if (\n    (elementEdgeEnd > scrollingEdgeEnd && elementSize < scrollingSize) ||\n    (elementEdgeStart < scrollingEdgeStart && elementSize > scrollingSize)\n  ) {\n    return elementEdgeEnd - scrollingEdgeEnd + scrollingBorderEnd\n  }\n\n  return 0\n}\n\nfunction getParentElement(element: Node): Element | null {\n  const parent = element.parentElement\n  if (parent == null) {\n    return (element.getRootNode() as ShadowRoot).host || null\n  }\n  return parent\n}\n\nexport default (target: Element, options: Options): CustomScrollAction[] => {\n  //TODO: remove this hack when microbundle will support typescript >= 4.0\n  const windowWithViewport = window as unknown as Window & {\n    visualViewport: visualViewport\n  }\n\n  const { scrollMode, block, inline, boundary, skipOverflowHiddenElements } =\n    options\n  // Allow using a callback to check the boundary\n  // The default behavior is to check if the current target matches the boundary element or not\n  // If undefined it'll check that target is never undefined (can happen as we recurse up the tree)\n  const checkBoundary =\n    typeof boundary === 'function' ? boundary : (node: any) => node !== boundary\n\n  if (!isElement(target)) {\n    throw new TypeError('Invalid target')\n  }\n\n  // Used to handle the top most element that can be scrolled\n  const scrollingElement = document.scrollingElement || document.documentElement\n\n  // Collect all the scrolling boxes, as defined in the spec: https://drafts.csswg.org/cssom-view/#scrolling-box\n  const frames: Element[] = []\n  let cursor: Element | null = target\n  while (isElement(cursor) && checkBoundary(cursor)) {\n    // Move cursor to parent\n    cursor = getParentElement(cursor)\n\n    // Stop when we reach the viewport\n    if (cursor === scrollingElement) {\n      frames.push(cursor)\n      break\n    }\n\n    // Skip document.body if it's not the scrollingElement and documentElement isn't independently scrollable\n    if (\n      cursor != null &&\n      cursor === document.body &&\n      isScrollable(cursor) &&\n      !isScrollable(document.documentElement)\n    ) {\n      continue\n    }\n\n    // Now we check if the element is scrollable, this code only runs if the loop haven't already hit the viewport or a custom boundary\n    if (cursor != null && isScrollable(cursor, skipOverflowHiddenElements)) {\n      frames.push(cursor)\n    }\n  }\n\n  // Support pinch-zooming properly, making sure elements scroll into the visual viewport\n  // Browsers that don't support visualViewport will report the layout viewport dimensions on document.documentElement.clientWidth/Height\n  // and viewport dimensions on window.innerWidth/Height\n  // https://www.quirksmode.org/mobile/viewports2.html\n  // https://bokand.github.io/viewport/index.html\n  const viewportWidth = windowWithViewport.visualViewport\n    ? windowWithViewport.visualViewport.width\n    : innerWidth\n  const viewportHeight = windowWithViewport.visualViewport\n    ? windowWithViewport.visualViewport.height\n    : innerHeight\n\n  // Newer browsers supports scroll[X|Y], page[X|Y]Offset is\n  const viewportX = window.scrollX || pageXOffset\n  const viewportY = window.scrollY || pageYOffset\n\n  const {\n    height: targetHeight,\n    width: targetWidth,\n    top: targetTop,\n    right: targetRight,\n    bottom: targetBottom,\n    left: targetLeft,\n  } = target.getBoundingClientRect()\n\n  // These values mutate as we loop through and generate scroll coordinates\n  let targetBlock: number =\n    block === 'start' || block === 'nearest'\n      ? targetTop\n      : block === 'end'\n      ? targetBottom\n      : targetTop + targetHeight / 2 // block === 'center\n  let targetInline: number =\n    inline === 'center'\n      ? targetLeft + targetWidth / 2\n      : inline === 'end'\n      ? targetRight\n      : targetLeft // inline === 'start || inline === 'nearest\n\n  // Collect new scroll positions\n  const computations: CustomScrollAction[] = []\n  // In chrome there's no longer a difference between caching the `frames.length` to a var or not, so we don't in this case (size > speed anyways)\n  for (let index = 0; index < frames.length; index++) {\n    const frame = frames[index]\n\n    // @TODO add a shouldScroll hook here that allows userland code to take control\n\n    const { height, width, top, right, bottom, left } =\n      frame.getBoundingClientRect()\n\n    // If the element is already visible we can end it here\n    // @TODO targetBlock and targetInline should be taken into account to be compliant with https://github.com/w3c/csswg-drafts/pull/1805/files#diff-3c17f0e43c20f8ecf89419d49e7ef5e0R1333\n    if (\n      scrollMode === 'if-needed' &&\n      targetTop >= 0 &&\n      targetLeft >= 0 &&\n      targetBottom <= viewportHeight &&\n      targetRight <= viewportWidth &&\n      targetTop >= top &&\n      targetBottom <= bottom &&\n      targetLeft >= left &&\n      targetRight <= right\n    ) {\n      // Break the loop and return the computations for things that are not fully visible\n      return computations\n    }\n\n    const frameStyle = getComputedStyle(frame)\n    const borderLeft = parseInt(frameStyle.borderLeftWidth as string, 10)\n    const borderTop = parseInt(frameStyle.borderTopWidth as string, 10)\n    const borderRight = parseInt(frameStyle.borderRightWidth as string, 10)\n    const borderBottom = parseInt(frameStyle.borderBottomWidth as string, 10)\n\n    let blockScroll: number = 0\n    let inlineScroll: number = 0\n\n    // The property existance checks for offfset[Width|Height] is because only HTMLElement objects have them, but any Element might pass by here\n    // @TODO find out if the \"as HTMLElement\" overrides can be dropped\n    const scrollbarWidth =\n      'offsetWidth' in frame\n        ? (frame as HTMLElement).offsetWidth -\n          (frame as HTMLElement).clientWidth -\n          borderLeft -\n          borderRight\n        : 0\n    const scrollbarHeight =\n      'offsetHeight' in frame\n        ? (frame as HTMLElement).offsetHeight -\n          (frame as HTMLElement).clientHeight -\n          borderTop -\n          borderBottom\n        : 0\n\n    const scaleX =\n      'offsetWidth' in frame\n        ? (frame as HTMLElement).offsetWidth === 0\n          ? 0\n          : width / (frame as HTMLElement).offsetWidth\n        : 0\n    const scaleY =\n      'offsetHeight' in frame\n        ? (frame as HTMLElement).offsetHeight === 0\n          ? 0\n          : height / (frame as HTMLElement).offsetHeight\n        : 0\n\n    if (scrollingElement === frame) {\n      // Handle viewport logic (document.documentElement or document.body)\n\n      if (block === 'start') {\n        blockScroll = targetBlock\n      } else if (block === 'end') {\n        blockScroll = targetBlock - viewportHeight\n      } else if (block === 'nearest') {\n        blockScroll = alignNearest(\n          viewportY,\n          viewportY + viewportHeight,\n          viewportHeight,\n          borderTop,\n          borderBottom,\n          viewportY + targetBlock,\n          viewportY + targetBlock + targetHeight,\n          targetHeight\n        )\n      } else {\n        // block === 'center' is the default\n        blockScroll = targetBlock - viewportHeight / 2\n      }\n\n      if (inline === 'start') {\n        inlineScroll = targetInline\n      } else if (inline === 'center') {\n        inlineScroll = targetInline - viewportWidth / 2\n      } else if (inline === 'end') {\n        inlineScroll = targetInline - viewportWidth\n      } else {\n        // inline === 'nearest' is the default\n        inlineScroll = alignNearest(\n          viewportX,\n          viewportX + viewportWidth,\n          viewportWidth,\n          borderLeft,\n          borderRight,\n          viewportX + targetInline,\n          viewportX + targetInline + targetWidth,\n          targetWidth\n        )\n      }\n\n      // Apply scroll position offsets and ensure they are within bounds\n      // @TODO add more test cases to cover this 100%\n      blockScroll = Math.max(0, blockScroll + viewportY)\n      inlineScroll = Math.max(0, inlineScroll + viewportX)\n    } else {\n      // Handle each scrolling frame that might exist between the target and the viewport\n\n      if (block === 'start') {\n        blockScroll = targetBlock - top - borderTop\n      } else if (block === 'end') {\n        blockScroll = targetBlock - bottom + borderBottom + scrollbarHeight\n      } else if (block === 'nearest') {\n        blockScroll = alignNearest(\n          top,\n          bottom,\n          height,\n          borderTop,\n          borderBottom + scrollbarHeight,\n          targetBlock,\n          targetBlock + targetHeight,\n          targetHeight\n        )\n      } else {\n        // block === 'center' is the default\n        blockScroll = targetBlock - (top + height / 2) + scrollbarHeight / 2\n      }\n\n      if (inline === 'start') {\n        inlineScroll = targetInline - left - borderLeft\n      } else if (inline === 'center') {\n        inlineScroll = targetInline - (left + width / 2) + scrollbarWidth / 2\n      } else if (inline === 'end') {\n        inlineScroll = targetInline - right + borderRight + scrollbarWidth\n      } else {\n        // inline === 'nearest' is the default\n        inlineScroll = alignNearest(\n          left,\n          right,\n          width,\n          borderLeft,\n          borderRight + scrollbarWidth,\n          targetInline,\n          targetInline + targetWidth,\n          targetWidth\n        )\n      }\n\n      const { scrollLeft, scrollTop } = frame\n      // Ensure scroll coordinates are not out of bounds while applying scroll offsets\n      blockScroll = Math.max(\n        0,\n        Math.min(\n          scrollTop + blockScroll / scaleY,\n          frame.scrollHeight - height / scaleY + scrollbarHeight\n        )\n      )\n      inlineScroll = Math.max(\n        0,\n        Math.min(\n          scrollLeft + inlineScroll / scaleX,\n          frame.scrollWidth - width / scaleX + scrollbarWidth\n        )\n      )\n\n      // Cache the offset so that parent frames can scroll this into view correctly\n      targetBlock += scrollTop - blockScroll\n      targetInline += scrollLeft - inlineScroll\n    }\n\n    computations.push({ el: frame, top: blockScroll, left: inlineScroll })\n  }\n\n  return computations\n}\n"], "names": ["el", "nodeType", "canOverflow", "overflow", "skipOverflowHiddenElements", "isScrollable", "clientHeight", "scrollHeight", "clientWidth", "scrollWidth", "style", "getComputedStyle", "overflowY", "overflowX", "frame", "ownerDocument", "defaultView", "frameElement", "e", "getFrameElement", "isHiddenByFrame", "scrollingEdgeStart", "scrollingEdgeEnd", "scrollingSize", "scrollingBorderStart", "scrollingBorderEnd", "elementEdgeStart", "elementEdgeEnd", "elementSize", "target", "options", "windowWithViewport", "window", "scrollMode", "block", "inline", "boundary", "checkBoundary", "node", "isElement", "element", "parent", "scrollingElement", "document", "documentElement", "frames", "cursor", "parentElement", "getRootNode", "host", "push", "body", "viewportWidth", "visualViewport", "width", "innerWidth", "viewportHeight", "height", "innerHeight", "scrollX", "pageXOffset", "viewportY", "scrollY", "pageYOffset", "_target$getBoundingCl", "getBoundingClientRect", "targetHeight", "targetWidth", "targetTop", "top", "targetRight", "right", "targetBottom", "bottom", "targetLeft", "left", "targetBlock", "targetInline", "computations", "index", "length", "_frame$getBoundingCli", "frameStyle", "borderLeft", "parseInt", "borderLeftWidth", "borderTop", "borderTopWidth", "borderRightWidth", "borderBottom", "borderBottomWidth", "blockScroll", "inlineScroll", "scrollbarWidth", "offsetWidth", "borderRight", "scrollbarHeight", "offsetHeight", "scaleX", "scaleY", "alignNearest", "viewportX", "Math", "max", "scrollLeft", "scrollTop", "min"], "mappings": "wOAuCA,WAAmBA,GACjB,MAAqB,iBAAPA,GAAyB,MAANA,GAA8B,IAAhBA,EAAGC,QACpD,CAEA,SAASC,EACPC,EACAC,GAEA,QAAIA,GAA2C,WAAbD,IAId,eAA0B,SAAbA,CACnC,CAyBA,SAAqBE,EAACL,EAAaI,GACjC,GAAIJ,EAAGM,aAAeN,EAAGO,cAAgBP,EAAGQ,YAAcR,EAAGS,YAAa,CACxE,IAAWC,EAAGC,iBAAiBX,EAAI,MACnC,OACaE,EAACQ,EAAME,UAAWR,IAC7BF,EAAYQ,EAAMG,UAAWT,IAhBnC,SAAyBJ,GACvB,IAAWc,EAbb,SAAyBd,GACvB,IAAKA,EAAGe,gBAAkBf,EAAGe,cAAcC,YACzC,OAAO,KAGT,IACE,OAAShB,EAACe,cAAcC,YAAYC,YAGrC,CAFC,MAAOC,GACP,OACD,IAAA,CACH,CAGgBC,CAAgBnB,GAC9B,QAAKc,IAKHA,EAAMR,aAAeN,EAAGO,cAAgBO,EAAMN,YAAcR,EAAGS,YAEnE,CAQMW,CAAgBpB,EAEnB,CAED,OAAO,CACT,CAUA,WACEqB,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,GAqBA,OACGF,EAAmBL,GAClBM,EAAiBL,GAClBI,EAAmBL,GAAsBM,EAAiBL,EAEpD,EA2CNI,GAAoBL,GAAsBO,GAAeL,GACzDI,GAAkBL,GAAoBM,GAAeL,EAE/CG,EAAmBL,EAAqBG,EA4C9CG,EAAiBL,GAAoBM,EAAcL,GACnDG,EAAmBL,GAAsBO,EAAcL,EAEjDI,EAAiBL,EAAmBG,EAI/C,CAAA,QAUA,SAAgBI,EAAiBC,GAE/B,IAAMC,EAAqBC,OAInBC,EACNH,EADMG,WAAYC,EAClBJ,EADkBI,MAAOC,EACzBL,EADyBK,OAAQC,EACjCN,EADiCM,SAAUhC,EAC3C0B,EAD2C1B,2BAKvCiC,EACgB,mBAAbD,EAA0BA,EAAW,SAACE,UAAkBA,IAAKF,CAAQ,EAE9E,IAAKG,EAAUV,GACb,MAAM,cAAc,kBAStB,IALA,IA3BwBW,EAClBC,EA0BgBC,EAAGC,SAASD,kBAAoBC,SAASC,gBAGnDC,EAAc,GAChBC,EAAmBjB,EACtBU,EAAUO,IAAWT,EAAcS,IAAS,CAKjD,IAHAA,EAhCY,OADRL,GADkBD,EAkCIM,GAjCLC,iBAELC,cAA6BC,MAAQ,UAkCtCP,EAAkB,CAC/BG,EAAOK,KAAKJ,GACZ,KACD,CAIW,MAAVA,GACAA,IAAWH,SAASQ,MACpB9C,EAAayC,KACZzC,EAAasC,SAASC,kBAMX,MAAVE,GAAkBzC,EAAayC,EAAQ1C,IACzCyC,EAAOK,KAAKJ,EAEf,CA4CD,IArCA,IAAmBM,EAAGrB,EAAmBsB,eACrCtB,EAAmBsB,eAAeC,MAClCC,WACgBC,EAAGzB,EAAmBsB,eACtCtB,EAAmBsB,eAAeI,OAClCC,cAGc1B,OAAO2B,SAAWC,YAC9BC,EAAY7B,OAAO8B,SAAWC,YAEpCC,EAOInC,EAAOoC,wBANDC,EAAYF,EAApBP,OACOU,EAAPb,EAAAA,MACKc,EAASJ,EAAdK,IACOC,EAAPC,EAAAA,MACQC,EAAYR,EAApBS,OACMC,EAANC,EAAAA,KAIEC,EACQ,UAAV1C,GAA+B,YAAVA,EACjBkC,EACU,QAAVlC,EACAsC,EACAJ,EAAYF,EAAe,EAC7BW,EACS,WAAX1C,EACIuC,EAAaP,EAAc,EAChB,QAAXhC,EACAmC,EACAI,EAGAI,EAAqC,KAE1B,EAAGC,EAAQlC,EAAOmC,OAAQD,IAAS,CAClD,IAAMjE,EAAQ+B,EAAOkC,GAKnBjE,EAAAA,EAAMmD,wBADAR,IAAAA,OAAQH,EAAK2B,EAAL3B,MAAOe,EAAAA,EAAAA,IAAKE,EAAKU,EAALV,MAAOE,EAAAA,EAAAA,OAAQE,IAAAA,KAK3C,GACiB,cAAf1C,GACAmC,GAAa,GACbM,GAAc,GACdF,GAAgBhB,GAChBc,GAAelB,GACfgB,GAAaC,GACbG,GAAgBC,GAChBC,GAAcC,GACdL,GAAeC,EAGf,OACDO,EAED,IAAMI,EAAavE,iBAAiBG,GAC9BqE,EAAaC,SAASF,EAAWG,gBAA2B,IACnDC,EAAGF,SAASF,EAAWK,eAA0B,MAC5CH,SAASF,EAAWM,iBAA4B,IAC9DC,EAAeL,SAASF,EAAWQ,kBAA6B,IAEvDC,EAAW,EACVC,EAAW,EAIPC,EAClB,gBAAiB/E,EACZA,EAAsBgF,YACtBhF,EAAsBN,YACvB2E,EACAY,EACA,EACAC,EACJ,mBACKlF,EAAsBmF,aACtBnF,EAAsBR,aACvBgF,EACAG,EACA,EAEAS,EACJ,gBAAsBpF,EACqB,IAAtCA,EAAsBgF,YACrB,EACAxC,EAASxC,EAAsBgF,YACjC,EACAK,EACJ,mBAC4C,IAAvCrF,EAAsBmF,aACrB,EACAxC,EAAU3C,EAAsBmF,aAClC,EAEN,GAAIvD,IAAqB5B,EAIrB6E,EADY,UAAVzD,EACY0C,EACK,QAAV1C,EACK0C,EAAcpB,EACT,YAAVtB,EACKkE,EACZvC,EACAA,EAAYL,EACZA,EACA8B,EACAG,EACA5B,EAAYe,EACZf,EAAYe,EAAcV,EAC1BA,GAIYU,EAAcpB,EAAiB,EAI7CoC,EADa,UAAXzD,EACa0C,EACK,WAAX1C,EACM0C,EAAezB,EAAgB,EAC1B,QAAXjB,EACM0C,EAAezB,EAGfgD,EACbC,EACAA,EAAYjD,EACZA,EACA+B,EACAY,EACAM,EAAYxB,EACZwB,EAAYxB,EAAeV,EAC3BA,GAMJwB,EAAcW,KAAKC,IAAI,EAAGZ,EAAc9B,GACxC+B,EAAeU,KAAKC,IAAI,EAAGX,EAAeS,OACrC,CAIHV,EADY,UAAVzD,EACY0C,EAAcP,EAAMiB,EACf,QAAVpD,EACK0C,EAAcH,EAASgB,EAAeO,EACjC,YAAV9D,EACKkE,EACZ/B,EACAI,EACAhB,EACA6B,EACAG,EAAeO,EACfpB,EACAA,EAAcV,EACdA,GAIYU,GAAeP,EAAMZ,EAAS,GAAKuC,EAAkB,EAInEJ,EADa,UAAXzD,EACa0C,EAAeF,EAAOQ,EACjB,WAAXhD,EACM0C,GAAgBF,EAAOrB,EAAQ,GAAKuC,EAAiB,EAChD,QAAX1D,EACM0C,EAAeN,EAAQwB,EAAcF,EAGrCO,EACbzB,EACAJ,EACAjB,EACA6B,EACAY,EAAcF,EACdhB,EACAA,EAAeV,EACfA,GAIJ,IAAQqC,EAA0B1F,EAA1B0F,WAAYC,EAAc3F,EAAd2F,UAkBpB7B,GAAe6B,GAhBfd,EAAcW,KAAKC,IACjB,EACAD,KAAKI,IACHD,EAAYd,EAAcQ,EAC1BrF,EAAMP,aAAekD,EAAS0C,EAASH,KAa3CnB,GAAgB2B,GAVhBZ,EAAeU,KAAKC,IAClB,EACAD,KAAKI,IACHF,EAAaZ,EAAeM,EAC5BpF,EAAML,YAAc6C,EAAQ4C,EAASL,IAO1C,CAEDf,EAAa5B,KAAK,CAAElD,GAAIc,EAAOuD,IAAKsB,EAAahB,KAAMiB,GACxD,CAED,QACD"}