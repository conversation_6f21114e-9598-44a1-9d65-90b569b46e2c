!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t||self).computeScrollIntoView=e()}(this,function(){function t(t){return"object"==typeof t&&null!=t&&1===t.nodeType}function e(t,e){return(!e||"hidden"!==t)&&"visible"!==t&&"clip"!==t}function n(t,n){if(t.clientHeight<t.scrollHeight||t.clientWidth<t.scrollWidth){var o=getComputedStyle(t,null);return e(o.overflowY,n)||e(o.overflowX,n)||function(t){var e=function(t){if(!t.ownerDocument||!t.ownerDocument.defaultView)return null;try{return t.ownerDocument.defaultView.frameElement}catch(t){return null}}(t);return!!e&&(e.clientHeight<t.scrollHeight||e.clientWidth<t.scrollWidth)}(t)}return!1}function o(t,e,n,o,i,r,l,f){return r<t&&l>e||r>t&&l<e?0:r<=t&&f<=n||l>=e&&f>=n?r-t-o:l>e&&f<n||r<t&&f>n?l-e+i:0}return function(e,i){var r=window,l=i.scrollMode,f=i.block,d=i.inline,u=i.boundary,h=i.skipOverflowHiddenElements,s="function"==typeof u?u:function(t){return t!==u};if(!t(e))throw new TypeError("Invalid target");for(var c,a,p=document.scrollingElement||document.documentElement,g=[],m=e;t(m)&&s(m);){if((m=null==(a=(c=m).parentElement)?c.getRootNode().host||null:a)===p){g.push(m);break}null!=m&&m===document.body&&n(m)&&!n(document.documentElement)||null!=m&&n(m,h)&&g.push(m)}for(var w=r.visualViewport?r.visualViewport.width:innerWidth,v=r.visualViewport?r.visualViewport.height:innerHeight,W=window.scrollX||pageXOffset,b=window.scrollY||pageYOffset,y=e.getBoundingClientRect(),H=y.height,E=y.width,M=y.top,V=y.right,x=y.bottom,I=y.left,T="start"===f||"nearest"===f?M:"end"===f?x:M+H/2,C="center"===d?I+E/2:"end"===d?V:I,R=[],k=0;k<g.length;k++){var B=g[k],D=B.getBoundingClientRect(),O=D.height,S=D.width,X=D.top,Y=D.right,j=D.bottom,L=D.left;if("if-needed"===l&&M>=0&&I>=0&&x<=v&&V<=w&&M>=X&&x<=j&&I>=L&&V<=Y)return R;var N=getComputedStyle(B),q=parseInt(N.borderLeftWidth,10),z=parseInt(N.borderTopWidth,10),A=parseInt(N.borderRightWidth,10),F=parseInt(N.borderBottomWidth,10),G=0,J=0,K="offsetWidth"in B?B.offsetWidth-B.clientWidth-q-A:0,P="offsetHeight"in B?B.offsetHeight-B.clientHeight-z-F:0,Q="offsetWidth"in B?0===B.offsetWidth?0:S/B.offsetWidth:0,U="offsetHeight"in B?0===B.offsetHeight?0:O/B.offsetHeight:0;if(p===B)G="start"===f?T:"end"===f?T-v:"nearest"===f?o(b,b+v,v,z,F,b+T,b+T+H,H):T-v/2,J="start"===d?C:"center"===d?C-w/2:"end"===d?C-w:o(W,W+w,w,q,A,W+C,W+C+E,E),G=Math.max(0,G+b),J=Math.max(0,J+W);else{G="start"===f?T-X-z:"end"===f?T-j+F+P:"nearest"===f?o(X,j,O,z,F+P,T,T+H,H):T-(X+O/2)+P/2,J="start"===d?C-L-q:"center"===d?C-(L+S/2)+K/2:"end"===d?C-Y+A+K:o(L,Y,S,q,A+K,C,C+E,E);var Z=B.scrollLeft,$=B.scrollTop;T+=$-(G=Math.max(0,Math.min($+G/U,B.scrollHeight-O/U+P))),C+=Z-(J=Math.max(0,Math.min(Z+J/Q,B.scrollWidth-S/Q+K)))}R.push({el:B,top:G,left:J})}return R}});
//# sourceMappingURL=compute-scroll-into-view.min.js.map
