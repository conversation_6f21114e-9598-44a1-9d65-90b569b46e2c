{"name": "array-tree-filter", "version": "2.1.0", "description": "filter in array tree", "main": "lib/index.js", "scripts": {"test": "npm run build && tape test/*.js", "pub": "npm run build && npm publish", "tsc": "tsc", "build": "npm run tsc && rollup index.js --output.format umd --name 'arrayTreeFilter' --output.file lib/index.js"}, "files": ["lib", "index.d.ts"], "repository": {"type": "git", "url": "git+ssh://**************/afc163/array-tree-filter.git"}, "keywords": ["array", "tree", "filter"], "author": "afc163 <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/afc163/array-tree-filter/issues"}, "homepage": "https://github.com/afc163/array-tree-filter#readme", "devDependencies": {"rollup": "^0.52.1", "tape": "^4.3.0", "typescript": "^2.7.1"}}