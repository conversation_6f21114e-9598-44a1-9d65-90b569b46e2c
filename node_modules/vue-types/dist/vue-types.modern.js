function e(e){return 1==(null!=(t=e)&&"object"==typeof t&&!1===Array.isArray(t))&&"[object Object]"===Object.prototype.toString.call(e);var t}const t=Object.prototype,r=t.toString,n=t.hasOwnProperty,o=/^\s*function (\w+)/;function i(e){var t;const r=null!==(t=null==e?void 0:e.type)&&void 0!==t?t:e;if(r){const e=r.toString().match(o);return e?e[1]:""}return""}const a=function(t){var r,n;return!1!==e(t)&&"function"==typeof(r=t.constructor)&&!1!==e(n=r.prototype)&&!1!==n.hasOwnProperty("isPrototypeOf")},s=e=>e;let u=s;if("production"!==process.env.NODE_ENV){const e="undefined"!=typeof console;u=e?function(e){console.warn("[VueTypes warn]: "+e)}:s}const c=(e,t)=>n.call(e,t),l=Number.isInteger||function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e},f=Array.isArray||function(e){return"[object Array]"===r.call(e)},d=e=>"[object Function]"===r.call(e),p=e=>a(e)&&c(e,"_vueTypes_name"),y=e=>a(e)&&(c(e,"type")||["_vueTypes_name","validator","default","required"].some(t=>c(e,t)));function v(e,t){return Object.defineProperty(e.bind(t),"__original",{value:e})}function h(e,t,r=!1){let n,s=!0,l="";n=a(e)?e:{type:e};const v=p(n)?n._vueTypes_name+" - ":"";if(y(n)&&null!==n.type){if(void 0===n.type||!0===n.type)return s;if(!n.required&&void 0===t)return s;f(n.type)?(s=n.type.some(e=>!0===h(e,t,!0)),l=n.type.map(e=>i(e)).join(" or ")):(l=i(n),s="Array"===l?f(t):"Object"===l?a(t):"String"===l||"Number"===l||"Boolean"===l||"Function"===l?function(e){if(null==e)return"";const t=e.constructor.toString().match(o);return t?t[1]:""}(t)===l:t instanceof n.type)}if(!s){const e=`${v}value "${t}" should be of type "${l}"`;return!1===r?(u(e),!1):e}if(c(n,"validator")&&d(n.validator)){const e=u,o=[];if(u=e=>{o.push(e)},s=n.validator(t),u=e,!s){const e=(o.length>1?"* ":"")+o.join("\n* ");return o.length=0,!1===r?(u(e),s):e}}return s}function b(e,t){const r=Object.defineProperties(t,{_vueTypes_name:{value:e,writable:!0},isRequired:{get(){return this.required=!0,this}},def:{value(e){return void 0!==e||this.default?d(e)||!0===h(this,e,!0)?(this.default=f(e)?()=>[...e]:a(e)?()=>Object.assign({},e):e,this):(u(`${this._vueTypes_name} - invalid default value: "${e}"`),this):this}}}),{validator:n}=r;return d(n)&&(r.validator=v(n,r)),r}function g(e,t){const r=b(e,t);return Object.defineProperty(r,"validate",{value(e){return d(this.validator)&&u(`${this._vueTypes_name} - calling .validate() will overwrite the current custom validator function. Validator info:\n${JSON.stringify(this)}`),this.validator=v(e,this),this}})}function O(e,t,r){const n=function(e){const t={};return Object.getOwnPropertyNames(e).forEach(r=>{t[r]=Object.getOwnPropertyDescriptor(e,r)}),Object.defineProperties({},t)}(t);if(n._vueTypes_name=e,!a(r))return n;const{validator:o,...i}=r;if(d(o)){let{validator:e}=n;e&&(e=null!==(u=(s=e).__original)&&void 0!==u?u:s),n.validator=v(e?function(t){return e.call(this,t)&&o.call(this,t)}:o,n)}var s,u;return Object.assign(n,i)}function m(e){return e.replace(/^(?!\s*$)/gm,"  ")}const j=()=>g("any",{}),_=()=>g("function",{type:Function}),T=()=>g("boolean",{type:Boolean}),w=()=>g("string",{type:String}),$=()=>g("number",{type:Number}),P=()=>g("array",{type:Array}),x=()=>g("object",{type:Object}),A=()=>b("integer",{type:Number,validator:e=>l(e)}),E=()=>b("symbol",{validator:e=>"symbol"==typeof e});function N(e,t="custom validation failed"){if("function"!=typeof e)throw new TypeError("[VueTypes error]: You must provide a function as argument");return b(e.name||"<<anonymous function>>",{validator(r){const n=e(r);return n||u(`${this._vueTypes_name} - ${t}`),n}})}function q(e){if(!f(e))throw new TypeError("[VueTypes error]: You must provide an array as argument.");const t=`oneOf - value should be one of "${e.join('", "')}".`,r=e.reduce((e,t)=>{if(null!=t){const r=t.constructor;-1===e.indexOf(r)&&e.push(r)}return e},[]);return b("oneOf",{type:r.length>0?r:void 0,validator(r){const n=-1!==e.indexOf(r);return n||u(t),n}})}function S(e){if(!f(e))throw new TypeError("[VueTypes error]: You must provide an array as argument");let t=!1,r=[];for(let n=0;n<e.length;n+=1){const o=e[n];if(y(o)){if(p(o)&&"oneOf"===o._vueTypes_name){r=r.concat(o.type);continue}if(d(o.validator)&&(t=!0),!0!==o.type&&o.type){r=r.concat(o.type);continue}}r.push(o)}return r=r.filter((e,t)=>r.indexOf(e)===t),b("oneOfType",t?{type:r,validator(t){const r=[],n=e.some(e=>{const n=h(p(e)&&"oneOf"===e._vueTypes_name?e.type||null:e,t,!0);return"string"==typeof n&&r.push(n),!0===n});return n||u(`oneOfType - provided value does not match any of the ${r.length} passed-in validators:\n${m(r.join("\n"))}`),n}}:{type:r})}function V(e){return b("arrayOf",{type:Array,validator(t){let r;const n=t.every(t=>(r=h(e,t,!0),!0===r));return n||u("arrayOf - value validation error:\n"+m(r)),n}})}function k(e){return b("instanceOf",{type:e})}function D(e){return b("objectOf",{type:Object,validator(t){let r;const n=Object.keys(t).every(n=>(r=h(e,t[n],!0),!0===r));return n||u("objectOf - value validation error:\n"+m(r)),n}})}function F(e){const t=Object.keys(e),r=t.filter(t=>{var r;return!!(null===(r=e[t])||void 0===r?void 0:r.required)}),n=b("shape",{type:Object,validator(n){if(!a(n))return!1;const o=Object.keys(n);if(r.length>0&&r.some(e=>-1===o.indexOf(e))){const e=r.filter(e=>-1===o.indexOf(e));return u(1===e.length?`shape - required property "${e[0]}" is not defined.`:`shape - required properties "${e.join('", "')}" are not defined.`),!1}return o.every(r=>{if(-1===t.indexOf(r))return!0===this._vueTypes_isLoose||(u(`shape - shape definition does not include a "${r}" property. Allowed keys: "${t.join('", "')}".`),!1);const o=h(e[r],n[r],!0);return"string"==typeof o&&u(`shape - "${r}" property validation error:\n ${m(o)}`),!0===o})}});return Object.defineProperty(n,"_vueTypes_isLoose",{writable:!0,value:!1}),Object.defineProperty(n,"loose",{get(){return this._vueTypes_isLoose=!0,this}}),n}class L{static get any(){return j()}static get func(){return _().def(this.defaults.func)}static get bool(){return T().def(this.defaults.bool)}static get string(){return w().def(this.defaults.string)}static get number(){return $().def(this.defaults.number)}static get array(){return P().def(this.defaults.array)}static get object(){return x().def(this.defaults.object)}static get integer(){return A().def(this.defaults.integer)}static get symbol(){return E()}static extend(e){if(f(e))return e.forEach(e=>this.extend(e)),this;const{name:t,validate:r=!1,getter:n=!1,...o}=e;if(c(this,t))throw new TypeError(`[VueTypes error]: Type "${t}" already defined`);const{type:i}=o;if(p(i))return delete o.type,Object.defineProperty(this,t,n?{get:()=>O(t,i,o)}:{value(...e){const r=O(t,i,o);return r.validator&&(r.validator=r.validator.bind(r,...e)),r}});let a;return a=n?{get(){const e=Object.assign({},o);return r?g(t,e):b(t,e)},enumerable:!0}:{value(...e){const n=Object.assign({},o);let i;return i=r?g(t,n):b(t,n),n.validator&&(i.validator=n.validator.bind(i,...e)),i},enumerable:!0},Object.defineProperty(this,t,a)}}function Y(e={func:()=>{},bool:!0,string:"",number:0,array:()=>[],object:()=>({}),integer:0}){var t;return(t=class extends L{static get sensibleDefaults(){return{...this.defaults}}static set sensibleDefaults(t){this.defaults=!1!==t?!0!==t?{...t}:{...e}:{}}}).defaults={...e},t}L.defaults={},L.custom=N,L.oneOf=q,L.instanceOf=k,L.oneOfType=S,L.arrayOf=V,L.objectOf=D,L.shape=F,L.utils={validate:(e,t)=>!0===h(t,e,!0),toType:(e,t,r=!1)=>r?g(e,t):b(e,t)};class B extends(Y()){}export default B;export{j as any,P as array,V as arrayOf,T as bool,Y as createTypes,N as custom,O as fromType,_ as func,k as instanceOf,A as integer,$ as number,x as object,D as objectOf,q as oneOf,S as oneOfType,F as shape,w as string,E as symbol,b as toType,g as toValidableType,h as validateType};
//# sourceMappingURL=vue-types.modern.js.map
