declare const TransButton: import("vue").DefineComponent<{
    noStyle: {
        type: BooleanConstructor;
        default: any;
    };
    onClick: FunctionConstructor;
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    autofocus: {
        type: BooleanConstructor;
        default: any;
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    noStyle: {
        type: BooleanConstructor;
        default: any;
    };
    onClick: FunctionConstructor;
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    autofocus: {
        type: BooleanConstructor;
        default: any;
    };
}>>, {
    disabled: boolean;
    noStyle: boolean;
    autofocus: boolean;
}>;
export default TransButton;
