declare const _default: import("vue").DefineComponent<{
    insertExtraNode: BooleanConstructor;
    disabled: BooleanConstructor;
}, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    insertExtraNode: BooleanConstructor;
    disabled: BooleanConstructor;
}>>, {
    disabled: boolean;
    insertExtraNode: boolean;
}>;
export default _default;
