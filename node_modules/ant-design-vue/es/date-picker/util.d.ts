import type { <PERSON>erM<PERSON> } from '../vc-picker/interface';
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './generatePicker';
export declare function getPlaceholder(picker: PickerMode | undefined, locale: PickerLocale, customizePlaceholder?: string): string;
export declare function getRangePlaceholder(picker: PickerMode | undefined, locale: PickerLocale, customizePlaceholder?: [string, string]): [string, string];
