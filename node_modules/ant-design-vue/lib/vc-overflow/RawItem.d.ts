declare const _default: import("vue").DefineComponent<{
    component: import("vue-types").VueTypeValidableDef<any>;
    title: import("vue-types").VueTypeValidableDef<any>;
    id: StringConstructor;
    onMouseenter: {
        type: FunctionConstructor;
    };
    onMouseleave: {
        type: FunctionConstructor;
    };
    onClick: {
        type: FunctionConstructor;
    };
    onKeydown: {
        type: FunctionConstructor;
    };
    onFocus: {
        type: FunctionConstructor;
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    component: import("vue-types").VueTypeValidableDef<any>;
    title: import("vue-types").VueTypeValidableDef<any>;
    id: StringConstructor;
    onMouseenter: {
        type: FunctionConstructor;
    };
    onMouseleave: {
        type: FunctionConstructor;
    };
    onClick: {
        type: FunctionConstructor;
    };
    onKeydown: {
        type: FunctionConstructor;
    };
    onFocus: {
        type: FunctionConstructor;
    };
}>>, {}>;
export default _default;
