declare const _default: import("vue").DefineComponent<{
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    changeSize: FunctionConstructor;
    quickGo: FunctionConstructor;
    selectComponentClass: import("vue-types").VueTypeValidableDef<any>;
    current: NumberConstructor;
    pageSizeOptions: import("vue-types").VueTypeValidableDef<unknown[]> & {
        default: () => unknown[];
    } & {
        default: () => unknown[];
    };
    pageSize: NumberConstructor;
    buildOptionText: FunctionConstructor;
    locale: import("vue-types").VueTypeValidableDef<{
        [key: string]: any;
    }> & {
        default: () => {
            [key: string]: any;
        };
    };
    rootPrefixCls: StringConstructor;
    selectPrefixCls: StringConstructor;
    goButton: import("vue-types").VueTypeValidableDef<any>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    changeSize: FunctionConstructor;
    quickGo: FunctionConstructor;
    selectComponentClass: import("vue-types").VueTypeValidableDef<any>;
    current: NumberConstructor;
    pageSizeOptions: import("vue-types").VueTypeValidableDef<unknown[]> & {
        default: () => unknown[];
    } & {
        default: () => unknown[];
    };
    pageSize: NumberConstructor;
    buildOptionText: FunctionConstructor;
    locale: import("vue-types").VueTypeValidableDef<{
        [key: string]: any;
    }> & {
        default: () => {
            [key: string]: any;
        };
    };
    rootPrefixCls: StringConstructor;
    selectPrefixCls: StringConstructor;
    goButton: import("vue-types").VueTypeValidableDef<any>;
}>>, {
    disabled: boolean;
    locale: {
        [key: string]: any;
    };
    pageSizeOptions: unknown[];
}>;
export default _default;
