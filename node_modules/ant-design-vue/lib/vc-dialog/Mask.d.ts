declare const _default: import("vue").DefineComponent<{
    prefixCls: StringConstructor;
    visible: BooleanConstructor;
    motionName: StringConstructor;
    maskProps: ObjectConstructor;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    prefixCls: StringConstructor;
    visible: BooleanConstructor;
    motionName: StringConstructor;
    maskProps: ObjectConstructor;
}>>, {
    visible: boolean;
}>;
export default _default;
