declare const placements: {
    topLeft: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
        offset: number[];
        targetOffset: number[];
    };
    topCenter: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
        offset: number[];
        targetOffset: number[];
    };
    topRight: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
        offset: number[];
        targetOffset: number[];
    };
    bottomLeft: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
        offset: number[];
        targetOffset: number[];
    };
    bottomCenter: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
        offset: number[];
        targetOffset: number[];
    };
    bottomRight: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
        offset: number[];
        targetOffset: number[];
    };
};
export default placements;
