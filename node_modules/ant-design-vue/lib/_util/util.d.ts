import type { VueNode } from './type';
export declare const isFunction: (val: any) => boolean;
export declare const controlDefaultValue: any;
export declare const isArray: (arg: any) => arg is any[];
export declare const isString: (val: any) => boolean;
export declare const isSymbol: (val: any) => boolean;
export declare const isObject: (val: any) => boolean;
declare const isOn: (key: any) => boolean;
declare const cacheStringFunction: (fn: any) => (str: any) => any;
declare const camelize: (str: any) => any;
declare const hyphenate: (str: any) => any;
declare const capitalize: (str: any) => any;
declare function resolvePropValue(options: any, props: any, key: any, value: any): any;
export declare function getDataAndAriaProps(props: any): {};
export declare function toPx(val: any): any;
export declare function renderHelper<T = Record<string, any>>(v: VueNode | ((arg0: T) => VueNode), props?: T, defaultV?: any): any;
export { isOn, cacheStringFunction, camelize, hyphenate, capitalize, resolvePropValue };
