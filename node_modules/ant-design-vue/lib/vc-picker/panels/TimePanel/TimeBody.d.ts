import type { GenerateConfig } from '../../generate';
import type { Locale, OnSelect } from '../../interface';
import type { SharedTimeProps } from '.';
import type { Ref } from 'vue';
export declare type BodyOperationRef = {
    onUpDown: (diff: number) => void;
};
export declare type TimeBodyProps<DateType> = {
    prefixCls: string;
    locale: Locale;
    generateConfig: GenerateConfig<DateType>;
    value?: DateType | null;
    onSelect: OnSelect<DateType>;
    activeColumnIndex: number;
    operationRef: Ref<BodyOperationRef | undefined>;
} & SharedTimeProps<DateType>;
declare const TimeBody: import("vue").DefineComponent<Readonly<{
    onSelect?: any;
    prefixCls?: any;
    generateConfig?: any;
    operationRef?: any;
    value?: any;
    hideDisabledOptions?: any;
    activeColumnIndex?: any;
    showHour?: any;
    showMinute?: any;
    showSecond?: any;
    use12Hours?: any;
    hourStep?: any;
    minuteStep?: any;
    secondStep?: any;
    disabledHours?: any;
    disabledMinutes?: any;
    disabledSeconds?: any;
    disabledTime?: any;
}>, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<Readonly<{
    onSelect?: any;
    prefixCls?: any;
    generateConfig?: any;
    operationRef?: any;
    value?: any;
    hideDisabledOptions?: any;
    activeColumnIndex?: any;
    showHour?: any;
    showMinute?: any;
    showSecond?: any;
    use12Hours?: any;
    hourStep?: any;
    minuteStep?: any;
    secondStep?: any;
    disabledHours?: any;
    disabledMinutes?: any;
    disabledSeconds?: any;
    disabledTime?: any;
}>>>, {
    readonly onSelect?: any;
    readonly prefixCls?: any;
    readonly generateConfig?: any;
    readonly operationRef?: any;
    readonly value?: any;
    readonly hideDisabledOptions?: any;
    readonly activeColumnIndex?: any;
    readonly showHour?: any;
    readonly showMinute?: any;
    readonly showSecond?: any;
    readonly use12Hours?: any;
    readonly hourStep?: any;
    readonly minuteStep?: any;
    readonly secondStep?: any;
    readonly disabledHours?: any;
    readonly disabledMinutes?: any;
    readonly disabledSeconds?: any;
    readonly disabledTime?: any;
}>;
export default TimeBody;
