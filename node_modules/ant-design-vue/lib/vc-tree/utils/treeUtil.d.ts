import type { DataNode, FlattenNode, DataEntity, Key, EventDataNode, GetKey, FieldNames, BasicDataNode } from '../interface';
import type { TreeNodeProps } from '../props';
import type { VueNode } from '../../_util/type';
export declare function getKey(key: Key, pos: string): Key;
export declare function fillFieldNames(fieldNames?: FieldNames): Required<FieldNames>;
/**
 * Warning if TreeNode do not provides key
 */
export declare function warningWithoutKey(treeData: DataNode[], fieldNames: FieldNames): void;
/**
 * Convert `children` of Tree into `treeData` structure.
 */
export declare function convertTreeToData(rootNodes: VueNode): DataNode[];
/**
 * Flat nest tree data into flatten list. This is used for virtual list render.
 * @param treeNodeList Origin data node list
 * @param expandedKeys
 * need expanded keys, provides `true` means all expanded (used in `rc-tree-select`).
 */
export declare function flattenTreeData(treeNodeList: DataNode[], expandedKeys: Key[] | true, fieldNames: FieldNames): FlattenNode[];
declare type ExternalGetKey = GetKey<DataNode> | string;
interface TraverseDataNodesConfig {
    childrenPropName?: string;
    externalGetKey?: ExternalGetKey;
    fieldNames?: FieldNames;
}
/**
 * Traverse all the data by `treeData`.
 * Please not use it out of the `rc-tree` since we may refactor this code.
 */
export declare function traverseDataNodes(dataNodes: DataNode[], callback: (data: {
    node: DataNode;
    index: number;
    pos: string;
    key: Key;
    parentPos: string | number;
    level: number;
    nodes: DataNode[];
}) => void, config?: TraverseDataNodesConfig | string): void;
interface Wrapper {
    posEntities: Record<string, DataEntity>;
    keyEntities: Record<Key, DataEntity>;
}
/**
 * Convert `treeData` into entity records.
 */
export declare function convertDataToEntities(dataNodes: DataNode[], { initWrapper, processEntity, onProcessFinished, externalGetKey, childrenPropName, fieldNames, }?: {
    initWrapper?: (wrapper: Wrapper) => Wrapper;
    processEntity?: (entity: DataEntity, wrapper: Wrapper) => void;
    onProcessFinished?: (wrapper: Wrapper) => void;
    externalGetKey?: ExternalGetKey;
    childrenPropName?: string;
    fieldNames?: FieldNames;
}, 
/** @deprecated Use `config.externalGetKey` instead */
legacyExternalGetKey?: ExternalGetKey): {
    posEntities: {};
    keyEntities: {};
};
export interface TreeNodeRequiredProps<TreeDataType extends BasicDataNode = DataNode> {
    expandedKeysSet: Set<Key>;
    selectedKeysSet: Set<Key>;
    loadedKeysSet: Set<Key>;
    loadingKeysSet: Set<Key>;
    checkedKeysSet: Set<Key>;
    halfCheckedKeysSet: Set<Key>;
    dragOverNodeKey: Key;
    dropPosition: number;
    keyEntities: Record<Key, DataEntity<TreeDataType>>;
}
/**
 * Get TreeNode props with Tree props.
 */
export declare function getTreeNodeProps<TreeDataType extends BasicDataNode = DataNode>(key: Key, { expandedKeysSet, selectedKeysSet, loadedKeysSet, loadingKeysSet, checkedKeysSet, halfCheckedKeysSet, dragOverNodeKey, dropPosition, keyEntities, }: TreeNodeRequiredProps<TreeDataType>): {
    eventKey: Key;
    expanded: boolean;
    selected: boolean;
    loaded: boolean;
    loading: boolean;
    checked: boolean;
    halfChecked: boolean;
    pos: string;
    parent: DataEntity<TreeDataType>;
    dragOver: boolean;
    dragOverGapTop: boolean;
    dragOverGapBottom: boolean;
};
export declare function convertNodePropsToEventData(props: TreeNodeProps & ReturnType<typeof getTreeNodeProps>): EventDataNode;
export {};
