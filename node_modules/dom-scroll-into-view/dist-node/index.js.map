{"version": 3, "file": "index.js", "sources": ["../dist-src/util.js", "../dist-src/index.js"], "sourcesContent": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _typeof(obj) { if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nvar RE_NUM = /[\\-+]?(?:\\d*\\.|)\\d+(?:[eE][\\-+]?\\d+|)/.source;\n\nfunction getClientPosition(elem) {\n  var box;\n  var x;\n  var y;\n  var doc = elem.ownerDocument;\n  var body = doc.body;\n  var docElem = doc && doc.documentElement; // 根据 GBS 最新数据，A-Grade Browsers 都已支持 getBoundingClientRect 方法，不用再考虑传统的实现方式\n\n  box = elem.getBoundingClientRect(); // 注：jQuery 还考虑减去 docElem.clientLeft/clientTop\n  // 但测试发现，这样反而会导致当 html 和 body 有边距/边框样式时，获取的值不正确\n  // 此外，ie6 会忽略 html 的 margin 值，幸运地是没有谁会去设置 html 的 margin\n\n  x = box.left;\n  y = box.top; // In IE, most of the time, 2 extra pixels are added to the top and left\n  // due to the implicit 2-pixel inset border.  In IE6/7 quirks mode and\n  // IE6 standards mode, this border can be overridden by setting the\n  // document element's border to zero -- thus, we cannot rely on the\n  // offset always being 2 pixels.\n  // In quirks mode, the offset can be determined by querying the body's\n  // clientLeft/clientTop, but in standards mode, it is found by querying\n  // the document element's clientLeft/clientTop.  Since we already called\n  // getClientBoundingRect we have already forced a reflow, so it is not\n  // too expensive just to query them all.\n  // ie 下应该减去窗口的边框吧，毕竟默认 absolute 都是相对窗口定位的\n  // 窗口边框标准是设 documentElement ,quirks 时设置 body\n  // 最好禁止在 body 和 html 上边框 ，但 ie < 9 html 默认有 2px ，减去\n  // 但是非 ie 不可能设置窗口边框，body html 也不是窗口 ,ie 可以通过 html,body 设置\n  // 标准 ie 下 docElem.clientTop 就是 border-top\n  // ie7 html 即窗口边框改变不了。永远为 2\n  // 但标准 firefox/chrome/ie9 下 docElem.clientTop 是窗口边框，即使设了 border-top 也为 0\n\n  x -= docElem.clientLeft || body.clientLeft || 0;\n  y -= docElem.clientTop || body.clientTop || 0;\n  return {\n    left: x,\n    top: y\n  };\n}\n\nfunction getScroll(w, top) {\n  var ret = w[\"page\".concat(top ? 'Y' : 'X', \"Offset\")];\n  var method = \"scroll\".concat(top ? 'Top' : 'Left');\n\n  if (typeof ret !== 'number') {\n    var d = w.document; // ie6,7,8 standard mode\n\n    ret = d.documentElement[method];\n\n    if (typeof ret !== 'number') {\n      // quirks mode\n      ret = d.body[method];\n    }\n  }\n\n  return ret;\n}\n\nfunction getScrollLeft(w) {\n  return getScroll(w);\n}\n\nfunction getScrollTop(w) {\n  return getScroll(w, true);\n}\n\nfunction getOffset(el) {\n  var pos = getClientPosition(el);\n  var doc = el.ownerDocument;\n  var w = doc.defaultView || doc.parentWindow;\n  pos.left += getScrollLeft(w);\n  pos.top += getScrollTop(w);\n  return pos;\n}\n\nfunction _getComputedStyle(elem, name, computedStyle_) {\n  var val = '';\n  var d = elem.ownerDocument;\n  var computedStyle = computedStyle_ || d.defaultView.getComputedStyle(elem, null); // https://github.com/kissyteam/kissy/issues/61\n\n  if (computedStyle) {\n    val = computedStyle.getPropertyValue(name) || computedStyle[name];\n  }\n\n  return val;\n}\n\nvar _RE_NUM_NO_PX = new RegExp(\"^(\".concat(RE_NUM, \")(?!px)[a-z%]+$\"), 'i');\n\nvar RE_POS = /^(top|right|bottom|left)$/;\nvar CURRENT_STYLE = 'currentStyle';\nvar RUNTIME_STYLE = 'runtimeStyle';\nvar LEFT = 'left';\nvar PX = 'px';\n\nfunction _getComputedStyleIE(elem, name) {\n  // currentStyle maybe null\n  // http://msdn.microsoft.com/en-us/library/ms535231.aspx\n  var ret = elem[CURRENT_STYLE] && elem[CURRENT_STYLE][name]; // 当 width/height 设置为百分比时，通过 pixelLeft 方式转换的 width/height 值\n  // 一开始就处理了! CUSTOM_STYLE.height,CUSTOM_STYLE.width ,cssHook 解决@2011-08-19\n  // 在 ie 下不对，需要直接用 offset 方式\n  // borderWidth 等值也有问题，但考虑到 borderWidth 设为百分比的概率很小，这里就不考虑了\n  // From the awesome hack by Dean Edwards\n  // http://erik.eae.net/archives/2007/07/27/18.54.15/#comment-102291\n  // If we're not dealing with a regular pixel number\n  // but a number that has a weird ending, we need to convert it to pixels\n  // exclude left right for relativity\n\n  if (_RE_NUM_NO_PX.test(ret) && !RE_POS.test(name)) {\n    // Remember the original values\n    var style = elem.style;\n    var left = style[LEFT];\n    var rsLeft = elem[RUNTIME_STYLE][LEFT]; // prevent flashing of content\n\n    elem[RUNTIME_STYLE][LEFT] = elem[CURRENT_STYLE][LEFT]; // Put in the new values to get a computed value out\n\n    style[LEFT] = name === 'fontSize' ? '1em' : ret || 0;\n    ret = style.pixelLeft + PX; // Revert the changed values\n\n    style[LEFT] = left;\n    elem[RUNTIME_STYLE][LEFT] = rsLeft;\n  }\n\n  return ret === '' ? 'auto' : ret;\n}\n\nvar getComputedStyleX;\n\nif (typeof window !== 'undefined') {\n  getComputedStyleX = window.getComputedStyle ? _getComputedStyle : _getComputedStyleIE;\n}\n\nfunction each(arr, fn) {\n  for (var i = 0; i < arr.length; i++) {\n    fn(arr[i]);\n  }\n}\n\nfunction isBorderBoxFn(elem) {\n  return getComputedStyleX(elem, 'boxSizing') === 'border-box';\n}\n\nvar BOX_MODELS = ['margin', 'border', 'padding'];\nvar CONTENT_INDEX = -1;\nvar PADDING_INDEX = 2;\nvar BORDER_INDEX = 1;\nvar MARGIN_INDEX = 0;\n\nfunction swap(elem, options, callback) {\n  var old = {};\n  var style = elem.style;\n  var name; // Remember the old values, and insert the new ones\n\n  for (name in options) {\n    if (options.hasOwnProperty(name)) {\n      old[name] = style[name];\n      style[name] = options[name];\n    }\n  }\n\n  callback.call(elem); // Revert the old values\n\n  for (name in options) {\n    if (options.hasOwnProperty(name)) {\n      style[name] = old[name];\n    }\n  }\n}\n\nfunction getPBMWidth(elem, props, which) {\n  var value = 0;\n  var prop;\n  var j;\n  var i;\n\n  for (j = 0; j < props.length; j++) {\n    prop = props[j];\n\n    if (prop) {\n      for (i = 0; i < which.length; i++) {\n        var cssProp = void 0;\n\n        if (prop === 'border') {\n          cssProp = \"\".concat(prop + which[i], \"Width\");\n        } else {\n          cssProp = prop + which[i];\n        }\n\n        value += parseFloat(getComputedStyleX(elem, cssProp)) || 0;\n      }\n    }\n  }\n\n  return value;\n}\n/**\n * A crude way of determining if an object is a window\n * @member util\n */\n\n\nfunction isWindow(obj) {\n  // must use == for ie8\n\n  /* eslint eqeqeq:0 */\n  return obj != null && obj == obj.window;\n}\n\nvar domUtils = {};\neach(['Width', 'Height'], function (name) {\n  domUtils[\"doc\".concat(name)] = function (refWin) {\n    var d = refWin.document;\n    return Math.max( // firefox chrome documentElement.scrollHeight< body.scrollHeight\n    // ie standard mode : documentElement.scrollHeight> body.scrollHeight\n    d.documentElement[\"scroll\".concat(name)], // quirks : documentElement.scrollHeight 最大等于可视窗口多一点？\n    d.body[\"scroll\".concat(name)], domUtils[\"viewport\".concat(name)](d));\n  };\n\n  domUtils[\"viewport\".concat(name)] = function (win) {\n    // pc browser includes scrollbar in window.innerWidth\n    var prop = \"client\".concat(name);\n    var doc = win.document;\n    var body = doc.body;\n    var documentElement = doc.documentElement;\n    var documentElementProp = documentElement[prop]; // 标准模式取 documentElement\n    // backcompat 取 body\n\n    return doc.compatMode === 'CSS1Compat' && documentElementProp || body && body[prop] || documentElementProp;\n  };\n});\n/*\n 得到元素的大小信息\n @param elem\n @param name\n @param {String} [extra]  'padding' : (css width) + padding\n 'border' : (css width) + padding + border\n 'margin' : (css width) + padding + border + margin\n */\n\nfunction getWH(elem, name, extra) {\n  if (isWindow(elem)) {\n    return name === 'width' ? domUtils.viewportWidth(elem) : domUtils.viewportHeight(elem);\n  } else if (elem.nodeType === 9) {\n    return name === 'width' ? domUtils.docWidth(elem) : domUtils.docHeight(elem);\n  }\n\n  var which = name === 'width' ? ['Left', 'Right'] : ['Top', 'Bottom'];\n  var borderBoxValue = name === 'width' ? elem.offsetWidth : elem.offsetHeight;\n  var computedStyle = getComputedStyleX(elem);\n  var isBorderBox = isBorderBoxFn(elem, computedStyle);\n  var cssBoxValue = 0;\n\n  if (borderBoxValue == null || borderBoxValue <= 0) {\n    borderBoxValue = undefined; // Fall back to computed then un computed css if necessary\n\n    cssBoxValue = getComputedStyleX(elem, name);\n\n    if (cssBoxValue == null || Number(cssBoxValue) < 0) {\n      cssBoxValue = elem.style[name] || 0;\n    } // Normalize '', auto, and prepare for extra\n\n\n    cssBoxValue = parseFloat(cssBoxValue) || 0;\n  }\n\n  if (extra === undefined) {\n    extra = isBorderBox ? BORDER_INDEX : CONTENT_INDEX;\n  }\n\n  var borderBoxValueOrIsBorderBox = borderBoxValue !== undefined || isBorderBox;\n  var val = borderBoxValue || cssBoxValue;\n\n  if (extra === CONTENT_INDEX) {\n    if (borderBoxValueOrIsBorderBox) {\n      return val - getPBMWidth(elem, ['border', 'padding'], which, computedStyle);\n    }\n\n    return cssBoxValue;\n  }\n\n  if (borderBoxValueOrIsBorderBox) {\n    var padding = extra === PADDING_INDEX ? -getPBMWidth(elem, ['border'], which, computedStyle) : getPBMWidth(elem, ['margin'], which, computedStyle);\n    return val + (extra === BORDER_INDEX ? 0 : padding);\n  }\n\n  return cssBoxValue + getPBMWidth(elem, BOX_MODELS.slice(extra), which, computedStyle);\n}\n\nvar cssShow = {\n  position: 'absolute',\n  visibility: 'hidden',\n  display: 'block'\n}; // fix #119 : https://github.com/kissyteam/kissy/issues/119\n\nfunction getWHIgnoreDisplay(elem) {\n  var val;\n  var args = arguments; // in case elem is window\n  // elem.offsetWidth === undefined\n\n  if (elem.offsetWidth !== 0) {\n    val = getWH.apply(undefined, args);\n  } else {\n    swap(elem, cssShow, function () {\n      val = getWH.apply(undefined, args);\n    });\n  }\n\n  return val;\n}\n\nfunction css(el, name, v) {\n  var value = v;\n\n  if (_typeof(name) === 'object') {\n    for (var i in name) {\n      if (name.hasOwnProperty(i)) {\n        css(el, i, name[i]);\n      }\n    }\n\n    return undefined;\n  }\n\n  if (typeof value !== 'undefined') {\n    if (typeof value === 'number') {\n      value += 'px';\n    }\n\n    el.style[name] = value;\n    return undefined;\n  }\n\n  return getComputedStyleX(el, name);\n}\n\neach(['width', 'height'], function (name) {\n  var first = name.charAt(0).toUpperCase() + name.slice(1);\n\n  domUtils[\"outer\".concat(first)] = function (el, includeMargin) {\n    return el && getWHIgnoreDisplay(el, name, includeMargin ? MARGIN_INDEX : BORDER_INDEX);\n  };\n\n  var which = name === 'width' ? ['Left', 'Right'] : ['Top', 'Bottom'];\n\n  domUtils[name] = function (elem, val) {\n    if (val !== undefined) {\n      if (elem) {\n        var computedStyle = getComputedStyleX(elem);\n        var isBorderBox = isBorderBoxFn(elem);\n\n        if (isBorderBox) {\n          val += getPBMWidth(elem, ['padding', 'border'], which, computedStyle);\n        }\n\n        return css(elem, name, val);\n      }\n\n      return undefined;\n    }\n\n    return elem && getWHIgnoreDisplay(elem, name, CONTENT_INDEX);\n  };\n}); // 设置 elem 相对 elem.ownerDocument 的坐标\n\nfunction setOffset(elem, offset) {\n  // set position first, in-case top/left are set even on static elem\n  if (css(elem, 'position') === 'static') {\n    elem.style.position = 'relative';\n  }\n\n  var old = getOffset(elem);\n  var ret = {};\n  var current;\n  var key;\n\n  for (key in offset) {\n    if (offset.hasOwnProperty(key)) {\n      current = parseFloat(css(elem, key)) || 0;\n      ret[key] = current + offset[key] - old[key];\n    }\n  }\n\n  css(elem, ret);\n}\n\nexport default _objectSpread({\n  getWindow: function getWindow(node) {\n    var doc = node.ownerDocument || node;\n    return doc.defaultView || doc.parentWindow;\n  },\n  offset: function offset(el, value) {\n    if (typeof value !== 'undefined') {\n      setOffset(el, value);\n    } else {\n      return getOffset(el);\n    }\n  },\n  isWindow: isWindow,\n  each: each,\n  css: css,\n  clone: function clone(obj) {\n    var ret = {};\n\n    for (var i in obj) {\n      if (obj.hasOwnProperty(i)) {\n        ret[i] = obj[i];\n      }\n    }\n\n    var overflow = obj.overflow;\n\n    if (overflow) {\n      for (var _i in obj) {\n        if (obj.hasOwnProperty(_i)) {\n          ret.overflow[_i] = obj.overflow[_i];\n        }\n      }\n    }\n\n    return ret;\n  },\n  scrollLeft: function scrollLeft(w, v) {\n    if (isWindow(w)) {\n      if (v === undefined) {\n        return getScrollLeft(w);\n      }\n\n      window.scrollTo(v, getScrollTop(w));\n    } else {\n      if (v === undefined) {\n        return w.scrollLeft;\n      }\n\n      w.scrollLeft = v;\n    }\n  },\n  scrollTop: function scrollTop(w, v) {\n    if (isWindow(w)) {\n      if (v === undefined) {\n        return getScrollTop(w);\n      }\n\n      window.scrollTo(getScrollLeft(w), v);\n    } else {\n      if (v === undefined) {\n        return w.scrollTop;\n      }\n\n      w.scrollTop = v;\n    }\n  },\n  viewportWidth: 0,\n  viewportHeight: 0\n}, domUtils);", "import util from \"./util.js\";\n\nfunction scrollIntoView(elem, container, config) {\n  config = config || {}; // document 归一化到 window\n\n  if (container.nodeType === 9) {\n    container = util.getWindow(container);\n  }\n\n  var allowHorizontalScroll = config.allowHorizontalScroll;\n  var onlyScrollIfNeeded = config.onlyScrollIfNeeded;\n  var alignWithTop = config.alignWithTop;\n  var alignWithLeft = config.alignWithLeft;\n  var offsetTop = config.offsetTop || 0;\n  var offsetLeft = config.offsetLeft || 0;\n  var offsetBottom = config.offsetBottom || 0;\n  var offsetRight = config.offsetRight || 0;\n  allowHorizontalScroll = allowHorizontalScroll === undefined ? true : allowHorizontalScroll;\n  var isWin = util.isWindow(container);\n  var elemOffset = util.offset(elem);\n  var eh = util.outerHeight(elem);\n  var ew = util.outerWidth(elem);\n  var containerOffset;\n  var ch;\n  var cw;\n  var containerScroll;\n  var diffTop;\n  var diffBottom;\n  var win;\n  var winScroll;\n  var ww;\n  var wh;\n\n  if (isWin) {\n    win = container;\n    wh = util.height(win);\n    ww = util.width(win);\n    winScroll = {\n      left: util.scrollLeft(win),\n      top: util.scrollTop(win)\n    }; // elem 相对 container 可视视窗的距离\n\n    diffTop = {\n      left: elemOffset.left - winScroll.left - offsetLeft,\n      top: elemOffset.top - winScroll.top - offsetTop\n    };\n    diffBottom = {\n      left: elemOffset.left + ew - (winScroll.left + ww) + offsetRight,\n      top: elemOffset.top + eh - (winScroll.top + wh) + offsetBottom\n    };\n    containerScroll = winScroll;\n  } else {\n    containerOffset = util.offset(container);\n    ch = container.clientHeight;\n    cw = container.clientWidth;\n    containerScroll = {\n      left: container.scrollLeft,\n      top: container.scrollTop\n    }; // elem 相对 container 可视视窗的距离\n    // 注意边框, offset 是边框到根节点\n\n    diffTop = {\n      left: elemOffset.left - (containerOffset.left + (parseFloat(util.css(container, 'borderLeftWidth')) || 0)) - offsetLeft,\n      top: elemOffset.top - (containerOffset.top + (parseFloat(util.css(container, 'borderTopWidth')) || 0)) - offsetTop\n    };\n    diffBottom = {\n      left: elemOffset.left + ew - (containerOffset.left + cw + (parseFloat(util.css(container, 'borderRightWidth')) || 0)) + offsetRight,\n      top: elemOffset.top + eh - (containerOffset.top + ch + (parseFloat(util.css(container, 'borderBottomWidth')) || 0)) + offsetBottom\n    };\n  }\n\n  if (diffTop.top < 0 || diffBottom.top > 0) {\n    // 强制向上\n    if (alignWithTop === true) {\n      util.scrollTop(container, containerScroll.top + diffTop.top);\n    } else if (alignWithTop === false) {\n      util.scrollTop(container, containerScroll.top + diffBottom.top);\n    } else {\n      // 自动调整\n      if (diffTop.top < 0) {\n        util.scrollTop(container, containerScroll.top + diffTop.top);\n      } else {\n        util.scrollTop(container, containerScroll.top + diffBottom.top);\n      }\n    }\n  } else {\n    if (!onlyScrollIfNeeded) {\n      alignWithTop = alignWithTop === undefined ? true : !!alignWithTop;\n\n      if (alignWithTop) {\n        util.scrollTop(container, containerScroll.top + diffTop.top);\n      } else {\n        util.scrollTop(container, containerScroll.top + diffBottom.top);\n      }\n    }\n  }\n\n  if (allowHorizontalScroll) {\n    if (diffTop.left < 0 || diffBottom.left > 0) {\n      // 强制向上\n      if (alignWithLeft === true) {\n        util.scrollLeft(container, containerScroll.left + diffTop.left);\n      } else if (alignWithLeft === false) {\n        util.scrollLeft(container, containerScroll.left + diffBottom.left);\n      } else {\n        // 自动调整\n        if (diffTop.left < 0) {\n          util.scrollLeft(container, containerScroll.left + diffTop.left);\n        } else {\n          util.scrollLeft(container, containerScroll.left + diffBottom.left);\n        }\n      }\n    } else {\n      if (!onlyScrollIfNeeded) {\n        alignWithLeft = alignWithLeft === undefined ? true : !!alignWithLeft;\n\n        if (alignWithLeft) {\n          util.scrollLeft(container, containerScroll.left + diffTop.left);\n        } else {\n          util.scrollLeft(container, containerScroll.left + diffBottom.left);\n        }\n      }\n    }\n  }\n}\n\nexport default scrollIntoView;"], "names": ["ownKeys", "object", "enumerableOnly", "keys", "Object", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "key", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "obj", "value", "configurable", "writable", "_typeof", "Symbol", "iterator", "constructor", "prototype", "RE_NUM", "getClientPosition", "elem", "box", "x", "y", "doc", "ownerDocument", "body", "doc<PERSON><PERSON>", "documentElement", "getBoundingClientRect", "left", "top", "clientLeft", "clientTop", "getScroll", "w", "ret", "concat", "method", "d", "document", "getScrollLeft", "getScrollTop", "getOffset", "el", "pos", "defaultView", "parentWindow", "_getComputedStyle", "name", "computedStyle_", "val", "computedStyle", "getComputedStyle", "getPropertyValue", "_RE_NUM_NO_PX", "RegExp", "RE_POS", "CURRENT_STYLE", "RUNTIME_STYLE", "LEFT", "PX", "_getComputedStyleIE", "test", "style", "rsLeft", "pixelLeft", "getComputedStyleX", "window", "each", "arr", "fn", "isBorderBoxFn", "BOX_MODELS", "CONTENT_INDEX", "PADDING_INDEX", "BORDER_INDEX", "MARGIN_INDEX", "swap", "options", "callback", "old", "hasOwnProperty", "call", "getPBMWidth", "props", "which", "prop", "j", "cssProp", "parseFloat", "isWindow", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "refWin", "Math", "max", "win", "documentElementProp", "compatMode", "getWH", "extra", "viewportWidth", "viewportHeight", "nodeType", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "doc<PERSON><PERSON>ght", "borderBoxValue", "offsetWidth", "offsetHeight", "isBorderBox", "cssBoxValue", "undefined", "Number", "borderBoxValueOrIsBorderBox", "padding", "slice", "cssShow", "position", "visibility", "display", "getWHIgnoreDisplay", "args", "css", "v", "first", "char<PERSON>t", "toUpperCase", "<PERSON><PERSON><PERSON><PERSON>", "setOffset", "offset", "current", "getWindow", "node", "clone", "overflow", "_i", "scrollLeft", "scrollTo", "scrollTop", "scrollIntoView", "container", "config", "util", "allowHorizontalScroll", "onlyScrollIfNeeded", "alignWithTop", "alignWithLeft", "offsetTop", "offsetLeft", "offsetBottom", "offsetRight", "isWin", "elemOffset", "eh", "outerHeight", "ew", "outerWidth", "containerOffset", "ch", "cw", "containerScroll", "diffTop", "diffBottom", "winScroll", "ww", "wh", "height", "width", "clientHeight", "clientWidth"], "mappings": ";;;;AAAA,SAASA,OAAT,CAAiBC,MAAjB,EAAyBC,cAAzB,EAAyC;MAAMC,IAAI,GAAGC,MAAM,CAACD,IAAP,CAAYF,MAAZ,CAAX;;MAAoCG,MAAM,CAACC,qBAAX,EAAkC;QAAMC,OAAO,GAAGF,MAAM,CAACC,qBAAP,CAA6BJ,MAA7B,CAAd;QAAwDC,cAAJ,EAAoBI,OAAO,GAAGA,OAAO,CAACC,MAAR,CAAe,UAAUC,GAAV,EAAe;aAASJ,MAAM,CAACK,wBAAP,CAAgCR,MAAhC,EAAwCO,GAAxC,EAA6CE,UAApD;KAAhC,CAAV;IAA8GP,IAAI,CAACQ,IAAL,CAAUC,KAAV,CAAgBT,IAAhB,EAAsBG,OAAtB;;;SAAyCH,IAAP;;;AAEvU,SAASU,aAAT,CAAuBC,MAAvB,EAA+B;OAAO,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGC,SAAS,CAACC,MAA9B,EAAsCF,CAAC,EAAvC,EAA2C;QAAMG,MAAM,GAAGF,SAAS,CAACD,CAAD,CAAT,IAAgB,IAAhB,GAAuBC,SAAS,CAACD,CAAD,CAAhC,GAAsC,EAAnD;;QAA2DA,CAAC,GAAG,CAAR,EAAW;MAAEf,OAAO,CAACkB,MAAD,EAAS,IAAT,CAAP,CAAsBC,OAAtB,CAA8B,UAAUC,GAAV,EAAe;QAAEC,eAAe,CAACP,MAAD,EAASM,GAAT,EAAcF,MAAM,CAACE,GAAD,CAApB,CAAf;OAA/C;KAAb,MAAkH,IAAIhB,MAAM,CAACkB,yBAAX,EAAsC;MAAElB,MAAM,CAACmB,gBAAP,CAAwBT,MAAxB,EAAgCV,MAAM,CAACkB,yBAAP,CAAiCJ,MAAjC,CAAhC;KAAxC,MAA0H;MAAElB,OAAO,CAACkB,MAAD,CAAP,CAAgBC,OAAhB,CAAwB,UAAUC,GAAV,EAAe;QAAEhB,MAAM,CAACoB,cAAP,CAAsBV,MAAtB,EAA8BM,GAA9B,EAAmChB,MAAM,CAACK,wBAAP,CAAgCS,MAAhC,EAAwCE,GAAxC,CAAnC;OAAzC;;;;SAA0IN,MAAP;;;AAEtf,SAASO,eAAT,CAAyBI,GAAzB,EAA8BL,GAA9B,EAAmCM,KAAnC,EAA0C;MAAMN,GAAG,IAAIK,GAAX,EAAgB;IAAErB,MAAM,CAACoB,cAAP,CAAsBC,GAAtB,EAA2BL,GAA3B,EAAgC;MAAEM,KAAK,EAAEA,KAAT;MAAgBhB,UAAU,EAAE,IAA5B;MAAkCiB,YAAY,EAAE,IAAhD;MAAsDC,QAAQ,EAAE;KAAhG;GAAlB,MAAkI;IAAEH,GAAG,CAACL,GAAD,CAAH,GAAWM,KAAX;;;SAA2BD,GAAP;;;AAEpM,SAASI,OAAT,CAAiBJ,GAAjB,EAAsB;MAAM,OAAOK,MAAP,KAAkB,UAAlB,IAAgC,OAAOA,MAAM,CAACC,QAAd,KAA2B,QAA/D,EAAyE;IAAEF,OAAO,GAAG,SAASA,OAAT,CAAiBJ,GAAjB,EAAsB;aAAS,OAAOA,GAAd;KAAlC;GAA3E,MAA0I;IAAEI,OAAO,GAAG,SAASA,OAAT,CAAiBJ,GAAjB,EAAsB;aAASA,GAAG,IAAI,OAAOK,MAAP,KAAkB,UAAzB,IAAuCL,GAAG,CAACO,WAAJ,KAAoBF,MAA3D,IAAqEL,GAAG,KAAKK,MAAM,CAACG,SAApF,GAAgG,QAAhG,GAA2G,OAAOR,GAAzH;KAAlC;;;SAA4KI,OAAO,CAACJ,GAAD,CAAd;;;AAEzU,IAAIS,MAAM,GAAG,wCAAwChB,MAArD;;AAEA,SAASiB,iBAAT,CAA2BC,IAA3B,EAAiC;MAC3BC,GAAJ;MACIC,CAAJ;MACIC,CAAJ;MACIC,GAAG,GAAGJ,IAAI,CAACK,aAAf;MACIC,IAAI,GAAGF,GAAG,CAACE,IAAf;MACIC,OAAO,GAAGH,GAAG,IAAIA,GAAG,CAACI,eAAzB,CAN+B;;EAQ/BP,GAAG,GAAGD,IAAI,CAACS,qBAAL,EAAN,CAR+B;;;;EAY/BP,CAAC,GAAGD,GAAG,CAACS,IAAR;EACAP,CAAC,GAAGF,GAAG,CAACU,GAAR,CAb+B;;;;;;;;;;;;;;;;;;EA+B/BT,CAAC,IAAIK,OAAO,CAACK,UAAR,IAAsBN,IAAI,CAACM,UAA3B,IAAyC,CAA9C;EACAT,CAAC,IAAII,OAAO,CAACM,SAAR,IAAqBP,IAAI,CAACO,SAA1B,IAAuC,CAA5C;SACO;IACLH,IAAI,EAAER,CADD;IAELS,GAAG,EAAER;GAFP;;;AAMF,SAASW,SAAT,CAAmBC,CAAnB,EAAsBJ,GAAtB,EAA2B;MACrBK,GAAG,GAAGD,CAAC,CAAC,OAAOE,MAAP,CAAcN,GAAG,GAAG,GAAH,GAAS,GAA1B,EAA+B,QAA/B,CAAD,CAAX;MACIO,MAAM,GAAG,SAASD,MAAT,CAAgBN,GAAG,GAAG,KAAH,GAAW,MAA9B,CAAb;;MAEI,OAAOK,GAAP,KAAe,QAAnB,EAA6B;QACvBG,CAAC,GAAGJ,CAAC,CAACK,QAAV,CAD2B;;IAG3BJ,GAAG,GAAGG,CAAC,CAACX,eAAF,CAAkBU,MAAlB,CAAN;;QAEI,OAAOF,GAAP,KAAe,QAAnB,EAA6B;;MAE3BA,GAAG,GAAGG,CAAC,CAACb,IAAF,CAAOY,MAAP,CAAN;;;;SAIGF,GAAP;;;AAGF,SAASK,aAAT,CAAuBN,CAAvB,EAA0B;SACjBD,SAAS,CAACC,CAAD,CAAhB;;;AAGF,SAASO,YAAT,CAAsBP,CAAtB,EAAyB;SAChBD,SAAS,CAACC,CAAD,EAAI,IAAJ,CAAhB;;;AAGF,SAASQ,SAAT,CAAmBC,EAAnB,EAAuB;MACjBC,GAAG,GAAG1B,iBAAiB,CAACyB,EAAD,CAA3B;MACIpB,GAAG,GAAGoB,EAAE,CAACnB,aAAb;MACIU,CAAC,GAAGX,GAAG,CAACsB,WAAJ,IAAmBtB,GAAG,CAACuB,YAA/B;EACAF,GAAG,CAACf,IAAJ,IAAYW,aAAa,CAACN,CAAD,CAAzB;EACAU,GAAG,CAACd,GAAJ,IAAWW,YAAY,CAACP,CAAD,CAAvB;SACOU,GAAP;;;AAGF,SAASG,iBAAT,CAA2B5B,IAA3B,EAAiC6B,IAAjC,EAAuCC,cAAvC,EAAuD;MACjDC,GAAG,GAAG,EAAV;MACIZ,CAAC,GAAGnB,IAAI,CAACK,aAAb;MACI2B,aAAa,GAAGF,cAAc,IAAIX,CAAC,CAACO,WAAF,CAAcO,gBAAd,CAA+BjC,IAA/B,EAAqC,IAArC,CAAtC,CAHqD;;MAKjDgC,aAAJ,EAAmB;IACjBD,GAAG,GAAGC,aAAa,CAACE,gBAAd,CAA+BL,IAA/B,KAAwCG,aAAa,CAACH,IAAD,CAA3D;;;SAGKE,GAAP;;;AAGF,IAAII,aAAa,GAAG,IAAIC,MAAJ,CAAW,KAAKnB,MAAL,CAAYnB,MAAZ,EAAoB,iBAApB,CAAX,EAAmD,GAAnD,CAApB;;AAEA,IAAIuC,MAAM,GAAG,2BAAb;AACA,IAAIC,aAAa,GAAG,cAApB;AACA,IAAIC,aAAa,GAAG,cAApB;AACA,IAAIC,IAAI,GAAG,MAAX;AACA,IAAIC,EAAE,GAAG,IAAT;;AAEA,SAASC,mBAAT,CAA6B1C,IAA7B,EAAmC6B,IAAnC,EAAyC;;;MAGnCb,GAAG,GAAGhB,IAAI,CAACsC,aAAD,CAAJ,IAAuBtC,IAAI,CAACsC,aAAD,CAAJ,CAAoBT,IAApB,CAAjC,CAHuC;;;;;;;;;;MAanCM,aAAa,CAACQ,IAAd,CAAmB3B,GAAnB,KAA2B,CAACqB,MAAM,CAACM,IAAP,CAAYd,IAAZ,CAAhC,EAAmD;;QAE7Ce,KAAK,GAAG5C,IAAI,CAAC4C,KAAjB;QACIlC,IAAI,GAAGkC,KAAK,CAACJ,IAAD,CAAhB;QACIK,MAAM,GAAG7C,IAAI,CAACuC,aAAD,CAAJ,CAAoBC,IAApB,CAAb,CAJiD;;IAMjDxC,IAAI,CAACuC,aAAD,CAAJ,CAAoBC,IAApB,IAA4BxC,IAAI,CAACsC,aAAD,CAAJ,CAAoBE,IAApB,CAA5B,CANiD;;IAQjDI,KAAK,CAACJ,IAAD,CAAL,GAAcX,IAAI,KAAK,UAAT,GAAsB,KAAtB,GAA8Bb,GAAG,IAAI,CAAnD;IACAA,GAAG,GAAG4B,KAAK,CAACE,SAAN,GAAkBL,EAAxB,CATiD;;IAWjDG,KAAK,CAACJ,IAAD,CAAL,GAAc9B,IAAd;IACAV,IAAI,CAACuC,aAAD,CAAJ,CAAoBC,IAApB,IAA4BK,MAA5B;;;SAGK7B,GAAG,KAAK,EAAR,GAAa,MAAb,GAAsBA,GAA7B;;;AAGF,IAAI+B,iBAAJ;;AAEA,IAAI,OAAOC,MAAP,KAAkB,WAAtB,EAAmC;EACjCD,iBAAiB,GAAGC,MAAM,CAACf,gBAAP,GAA0BL,iBAA1B,GAA8Cc,mBAAlE;;;AAGF,SAASO,IAAT,CAAcC,GAAd,EAAmBC,EAAnB,EAAuB;OAChB,IAAIxE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGuE,GAAG,CAACrE,MAAxB,EAAgCF,CAAC,EAAjC,EAAqC;IACnCwE,EAAE,CAACD,GAAG,CAACvE,CAAD,CAAJ,CAAF;;;;AAIJ,SAASyE,aAAT,CAAuBpD,IAAvB,EAA6B;SACpB+C,iBAAiB,CAAC/C,IAAD,EAAO,WAAP,CAAjB,KAAyC,YAAhD;;;AAGF,IAAIqD,UAAU,GAAG,CAAC,QAAD,EAAW,QAAX,EAAqB,SAArB,CAAjB;AACA,IAAIC,aAAa,GAAG,CAAC,CAArB;AACA,IAAIC,aAAa,GAAG,CAApB;AACA,IAAIC,YAAY,GAAG,CAAnB;AACA,IAAIC,YAAY,GAAG,CAAnB;;AAEA,SAASC,IAAT,CAAc1D,IAAd,EAAoB2D,OAApB,EAA6BC,QAA7B,EAAuC;MACjCC,GAAG,GAAG,EAAV;MACIjB,KAAK,GAAG5C,IAAI,CAAC4C,KAAjB;MACIf,IAAJ,CAHqC;;OAKhCA,IAAL,IAAa8B,OAAb,EAAsB;QAChBA,OAAO,CAACG,cAAR,CAAuBjC,IAAvB,CAAJ,EAAkC;MAChCgC,GAAG,CAAChC,IAAD,CAAH,GAAYe,KAAK,CAACf,IAAD,CAAjB;MACAe,KAAK,CAACf,IAAD,CAAL,GAAc8B,OAAO,CAAC9B,IAAD,CAArB;;;;EAIJ+B,QAAQ,CAACG,IAAT,CAAc/D,IAAd,EAZqC;;OAchC6B,IAAL,IAAa8B,OAAb,EAAsB;QAChBA,OAAO,CAACG,cAAR,CAAuBjC,IAAvB,CAAJ,EAAkC;MAChCe,KAAK,CAACf,IAAD,CAAL,GAAcgC,GAAG,CAAChC,IAAD,CAAjB;;;;;AAKN,SAASmC,WAAT,CAAqBhE,IAArB,EAA2BiE,KAA3B,EAAkCC,KAAlC,EAAyC;MACnC5E,KAAK,GAAG,CAAZ;MACI6E,IAAJ;MACIC,CAAJ;MACIzF,CAAJ;;OAEKyF,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGH,KAAK,CAACpF,MAAtB,EAA8BuF,CAAC,EAA/B,EAAmC;IACjCD,IAAI,GAAGF,KAAK,CAACG,CAAD,CAAZ;;QAEID,IAAJ,EAAU;WACHxF,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGuF,KAAK,CAACrF,MAAtB,EAA8BF,CAAC,EAA/B,EAAmC;YAC7B0F,OAAO,GAAG,KAAK,CAAnB;;YAEIF,IAAI,KAAK,QAAb,EAAuB;UACrBE,OAAO,GAAG,GAAGpD,MAAH,CAAUkD,IAAI,GAAGD,KAAK,CAACvF,CAAD,CAAtB,EAA2B,OAA3B,CAAV;SADF,MAEO;UACL0F,OAAO,GAAGF,IAAI,GAAGD,KAAK,CAACvF,CAAD,CAAtB;;;QAGFW,KAAK,IAAIgF,UAAU,CAACvB,iBAAiB,CAAC/C,IAAD,EAAOqE,OAAP,CAAlB,CAAV,IAAgD,CAAzD;;;;;SAKC/E,KAAP;;;;;;;;AAQF,SAASiF,QAAT,CAAkBlF,GAAlB,EAAuB;;;;SAIdA,GAAG,IAAI,IAAP,IAAeA,GAAG,IAAIA,GAAG,CAAC2D,MAAjC;;;AAGF,IAAIwB,QAAQ,GAAG,EAAf;AACAvB,IAAI,CAAC,CAAC,OAAD,EAAU,QAAV,CAAD,EAAsB,UAAUpB,IAAV,EAAgB;EACxC2C,QAAQ,CAAC,MAAMvD,MAAN,CAAaY,IAAb,CAAD,CAAR,GAA+B,UAAU4C,MAAV,EAAkB;QAC3CtD,CAAC,GAAGsD,MAAM,CAACrD,QAAf;WACOsD,IAAI,CAACC,GAAL;;IAEPxD,CAAC,CAACX,eAAF,CAAkB,SAASS,MAAT,CAAgBY,IAAhB,CAAlB,CAFO;IAGPV,CAAC,CAACb,IAAF,CAAO,SAASW,MAAT,CAAgBY,IAAhB,CAAP,CAHO,EAGwB2C,QAAQ,CAAC,WAAWvD,MAAX,CAAkBY,IAAlB,CAAD,CAAR,CAAkCV,CAAlC,CAHxB,CAAP;GAFF;;EAQAqD,QAAQ,CAAC,WAAWvD,MAAX,CAAkBY,IAAlB,CAAD,CAAR,GAAoC,UAAU+C,GAAV,EAAe;;QAE7CT,IAAI,GAAG,SAASlD,MAAT,CAAgBY,IAAhB,CAAX;QACIzB,GAAG,GAAGwE,GAAG,CAACxD,QAAd;QACId,IAAI,GAAGF,GAAG,CAACE,IAAf;QACIE,eAAe,GAAGJ,GAAG,CAACI,eAA1B;QACIqE,mBAAmB,GAAGrE,eAAe,CAAC2D,IAAD,CAAzC,CANiD;;;WAS1C/D,GAAG,CAAC0E,UAAJ,KAAmB,YAAnB,IAAmCD,mBAAnC,IAA0DvE,IAAI,IAAIA,IAAI,CAAC6D,IAAD,CAAtE,IAAgFU,mBAAvF;GATF;CATE,CAAJ;;;;;;;;;;AA8BA,SAASE,KAAT,CAAe/E,IAAf,EAAqB6B,IAArB,EAA2BmD,KAA3B,EAAkC;MAC5BT,QAAQ,CAACvE,IAAD,CAAZ,EAAoB;WACX6B,IAAI,KAAK,OAAT,GAAmB2C,QAAQ,CAACS,aAAT,CAAuBjF,IAAvB,CAAnB,GAAkDwE,QAAQ,CAACU,cAAT,CAAwBlF,IAAxB,CAAzD;GADF,MAEO,IAAIA,IAAI,CAACmF,QAAL,KAAkB,CAAtB,EAAyB;WACvBtD,IAAI,KAAK,OAAT,GAAmB2C,QAAQ,CAACY,QAAT,CAAkBpF,IAAlB,CAAnB,GAA6CwE,QAAQ,CAACa,SAAT,CAAmBrF,IAAnB,CAApD;;;MAGEkE,KAAK,GAAGrC,IAAI,KAAK,OAAT,GAAmB,CAAC,MAAD,EAAS,OAAT,CAAnB,GAAuC,CAAC,KAAD,EAAQ,QAAR,CAAnD;MACIyD,cAAc,GAAGzD,IAAI,KAAK,OAAT,GAAmB7B,IAAI,CAACuF,WAAxB,GAAsCvF,IAAI,CAACwF,YAAhE;MACIxD,aAAa,GAAGe,iBAAiB,CAAC/C,IAAD,CAArC;MACIyF,WAAW,GAAGrC,aAAa,CAACpD,IAAD,AAAA,CAA/B;MACI0F,WAAW,GAAG,CAAlB;;MAEIJ,cAAc,IAAI,IAAlB,IAA0BA,cAAc,IAAI,CAAhD,EAAmD;IACjDA,cAAc,GAAGK,SAAjB,CADiD;;IAGjDD,WAAW,GAAG3C,iBAAiB,CAAC/C,IAAD,EAAO6B,IAAP,CAA/B;;QAEI6D,WAAW,IAAI,IAAf,IAAuBE,MAAM,CAACF,WAAD,CAAN,GAAsB,CAAjD,EAAoD;MAClDA,WAAW,GAAG1F,IAAI,CAAC4C,KAAL,CAAWf,IAAX,KAAoB,CAAlC;KAN+C;;;IAUjD6D,WAAW,GAAGpB,UAAU,CAACoB,WAAD,CAAV,IAA2B,CAAzC;;;MAGEV,KAAK,KAAKW,SAAd,EAAyB;IACvBX,KAAK,GAAGS,WAAW,GAAGjC,YAAH,GAAkBF,aAArC;;;MAGEuC,2BAA2B,GAAGP,cAAc,KAAKK,SAAnB,IAAgCF,WAAlE;MACI1D,GAAG,GAAGuD,cAAc,IAAII,WAA5B;;MAEIV,KAAK,KAAK1B,aAAd,EAA6B;QACvBuC,2BAAJ,EAAiC;aACxB9D,GAAG,GAAGiC,WAAW,CAAChE,IAAD,EAAO,CAAC,QAAD,EAAW,SAAX,CAAP,EAA8BkE,KAA9B,AAAA,CAAxB;;;WAGKwB,WAAP;;;MAGEG,2BAAJ,EAAiC;QAC3BC,OAAO,GAAGd,KAAK,KAAKzB,aAAV,GAA0B,CAACS,WAAW,CAAChE,IAAD,EAAO,CAAC,QAAD,CAAP,EAAmBkE,KAAnB,AAAA,CAAtC,GAAiFF,WAAW,CAAChE,IAAD,EAAO,CAAC,QAAD,CAAP,EAAmBkE,KAAnB,AAAA,CAA1G;WACOnC,GAAG,IAAIiD,KAAK,KAAKxB,YAAV,GAAyB,CAAzB,GAA6BsC,OAAjC,CAAV;;;SAGKJ,WAAW,GAAG1B,WAAW,CAAChE,IAAD,EAAOqD,UAAU,CAAC0C,KAAX,CAAiBf,KAAjB,CAAP,EAAgCd,KAAhC,AAAA,CAAhC;;;AAGF,IAAI8B,OAAO,GAAG;EACZC,QAAQ,EAAE,UADE;EAEZC,UAAU,EAAE,QAFA;EAGZC,OAAO,EAAE;CAHX;;AAMA,SAASC,kBAAT,CAA4BpG,IAA5B,EAAkC;MAC5B+B,GAAJ;MACIsE,IAAI,GAAGzH,SAAX,CAFgC;;;MAK5BoB,IAAI,CAACuF,WAAL,KAAqB,CAAzB,EAA4B;IAC1BxD,GAAG,GAAGgD,KAAK,CAACvG,KAAN,CAAYmH,SAAZ,EAAuBU,IAAvB,CAAN;GADF,MAEO;IACL3C,IAAI,CAAC1D,IAAD,EAAOgG,OAAP,EAAgB,YAAY;MAC9BjE,GAAG,GAAGgD,KAAK,CAACvG,KAAN,CAAYmH,SAAZ,EAAuBU,IAAvB,CAAN;KADE,CAAJ;;;SAKKtE,GAAP;;;AAGF,SAASuE,GAAT,CAAa9E,EAAb,EAAiBK,IAAjB,EAAuB0E,CAAvB,EAA0B;MACpBjH,KAAK,GAAGiH,CAAZ;;MAEI9G,OAAO,CAACoC,IAAD,CAAP,KAAkB,QAAtB,EAAgC;SACzB,IAAIlD,CAAT,IAAckD,IAAd,EAAoB;UACdA,IAAI,CAACiC,cAAL,CAAoBnF,CAApB,CAAJ,EAA4B;QAC1B2H,GAAG,CAAC9E,EAAD,EAAK7C,CAAL,EAAQkD,IAAI,CAAClD,CAAD,CAAZ,CAAH;;;;WAIGgH,SAAP;;;MAGE,OAAOrG,KAAP,KAAiB,WAArB,EAAkC;QAC5B,OAAOA,KAAP,KAAiB,QAArB,EAA+B;MAC7BA,KAAK,IAAI,IAAT;;;IAGFkC,EAAE,CAACoB,KAAH,CAASf,IAAT,IAAiBvC,KAAjB;WACOqG,SAAP;;;SAGK5C,iBAAiB,CAACvB,EAAD,EAAKK,IAAL,CAAxB;;;AAGFoB,IAAI,CAAC,CAAC,OAAD,EAAU,QAAV,CAAD,EAAsB,UAAUpB,IAAV,EAAgB;MACpC2E,KAAK,GAAG3E,IAAI,CAAC4E,MAAL,CAAY,CAAZ,EAAeC,WAAf,KAA+B7E,IAAI,CAACkE,KAAL,CAAW,CAAX,CAA3C;;EAEAvB,QAAQ,CAAC,QAAQvD,MAAR,CAAeuF,KAAf,CAAD,CAAR,GAAkC,UAAUhF,EAAV,EAAcmF,aAAd,EAA6B;WACtDnF,EAAE,IAAI4E,kBAAkB,CAAC5E,EAAD,EAAKK,IAAL,EAAW8E,aAAa,GAAGlD,YAAH,GAAkBD,YAA1C,CAA/B;GADF;;MAIIU,KAAK,GAAGrC,IAAI,KAAK,OAAT,GAAmB,CAAC,MAAD,EAAS,OAAT,CAAnB,GAAuC,CAAC,KAAD,EAAQ,QAAR,CAAnD;;EAEA2C,QAAQ,CAAC3C,IAAD,CAAR,GAAiB,UAAU7B,IAAV,EAAgB+B,GAAhB,EAAqB;QAChCA,GAAG,KAAK4D,SAAZ,EAAuB;UACjB3F,IAAJ,EAAU;YACJgC,aAAa,GAAGe,iBAAiB,CAAC/C,IAAD,CAArC;YACIyF,WAAW,GAAGrC,aAAa,CAACpD,IAAD,CAA/B;;YAEIyF,WAAJ,EAAiB;UACf1D,GAAG,IAAIiC,WAAW,CAAChE,IAAD,EAAO,CAAC,SAAD,EAAY,QAAZ,CAAP,EAA8BkE,KAA9B,AAAA,CAAlB;;;eAGKoC,GAAG,CAACtG,IAAD,EAAO6B,IAAP,EAAaE,GAAb,CAAV;;;aAGK4D,SAAP;;;WAGK3F,IAAI,IAAIoG,kBAAkB,CAACpG,IAAD,EAAO6B,IAAP,EAAayB,aAAb,CAAjC;GAhBF;CATE,CAAJ;;AA6BA,SAASsD,SAAT,CAAmB5G,IAAnB,EAAyB6G,MAAzB,EAAiC;;MAE3BP,GAAG,CAACtG,IAAD,EAAO,UAAP,CAAH,KAA0B,QAA9B,EAAwC;IACtCA,IAAI,CAAC4C,KAAL,CAAWqD,QAAX,GAAsB,UAAtB;;;MAGEpC,GAAG,GAAGtC,SAAS,CAACvB,IAAD,CAAnB;MACIgB,GAAG,GAAG,EAAV;MACI8F,OAAJ;MACI9H,GAAJ;;OAEKA,GAAL,IAAY6H,MAAZ,EAAoB;QACdA,MAAM,CAAC/C,cAAP,CAAsB9E,GAAtB,CAAJ,EAAgC;MAC9B8H,OAAO,GAAGxC,UAAU,CAACgC,GAAG,CAACtG,IAAD,EAAOhB,GAAP,CAAJ,CAAV,IAA8B,CAAxC;MACAgC,GAAG,CAAChC,GAAD,CAAH,GAAW8H,OAAO,GAAGD,MAAM,CAAC7H,GAAD,CAAhB,GAAwB6E,GAAG,CAAC7E,GAAD,CAAtC;;;;EAIJsH,GAAG,CAACtG,IAAD,EAAOgB,GAAP,CAAH;;;AAGF,WAAevC,aAAa,CAAC;EAC3BsI,SAAS,EAAE,SAASA,SAAT,CAAmBC,IAAnB,EAAyB;QAC9B5G,GAAG,GAAG4G,IAAI,CAAC3G,aAAL,IAAsB2G,IAAhC;WACO5G,GAAG,CAACsB,WAAJ,IAAmBtB,GAAG,CAACuB,YAA9B;GAHyB;EAK3BkF,MAAM,EAAE,SAASA,MAAT,CAAgBrF,EAAhB,EAAoBlC,KAApB,EAA2B;QAC7B,OAAOA,KAAP,KAAiB,WAArB,EAAkC;MAChCsH,SAAS,CAACpF,EAAD,EAAKlC,KAAL,CAAT;KADF,MAEO;aACEiC,SAAS,CAACC,EAAD,CAAhB;;GATuB;EAY3B+C,QAAQ,EAAEA,QAZiB;EAa3BtB,IAAI,EAAEA,IAbqB;EAc3BqD,GAAG,EAAEA,GAdsB;EAe3BW,KAAK,EAAE,SAASA,KAAT,CAAe5H,GAAf,EAAoB;QACrB2B,GAAG,GAAG,EAAV;;SAEK,IAAIrC,CAAT,IAAcU,GAAd,EAAmB;UACbA,GAAG,CAACyE,cAAJ,CAAmBnF,CAAnB,CAAJ,EAA2B;QACzBqC,GAAG,CAACrC,CAAD,CAAH,GAASU,GAAG,CAACV,CAAD,CAAZ;;;;QAIAuI,QAAQ,GAAG7H,GAAG,CAAC6H,QAAnB;;QAEIA,QAAJ,EAAc;WACP,IAAIC,EAAT,IAAe9H,GAAf,EAAoB;YACdA,GAAG,CAACyE,cAAJ,CAAmBqD,EAAnB,CAAJ,EAA4B;UAC1BnG,GAAG,CAACkG,QAAJ,CAAaC,EAAb,IAAmB9H,GAAG,CAAC6H,QAAJ,CAAaC,EAAb,CAAnB;;;;;WAKCnG,GAAP;GAlCyB;EAoC3BoG,UAAU,EAAE,SAASA,UAAT,CAAoBrG,CAApB,EAAuBwF,CAAvB,EAA0B;QAChChC,QAAQ,CAACxD,CAAD,CAAZ,EAAiB;UACXwF,CAAC,KAAKZ,SAAV,EAAqB;eACZtE,aAAa,CAACN,CAAD,CAApB;;;MAGFiC,MAAM,CAACqE,QAAP,CAAgBd,CAAhB,EAAmBjF,YAAY,CAACP,CAAD,CAA/B;KALF,MAMO;UACDwF,CAAC,KAAKZ,SAAV,EAAqB;eACZ5E,CAAC,CAACqG,UAAT;;;MAGFrG,CAAC,CAACqG,UAAF,GAAeb,CAAf;;GAhDuB;EAmD3Be,SAAS,EAAE,SAASA,SAAT,CAAmBvG,CAAnB,EAAsBwF,CAAtB,EAAyB;QAC9BhC,QAAQ,CAACxD,CAAD,CAAZ,EAAiB;UACXwF,CAAC,KAAKZ,SAAV,EAAqB;eACZrE,YAAY,CAACP,CAAD,CAAnB;;;MAGFiC,MAAM,CAACqE,QAAP,CAAgBhG,aAAa,CAACN,CAAD,CAA7B,EAAkCwF,CAAlC;KALF,MAMO;UACDA,CAAC,KAAKZ,SAAV,EAAqB;eACZ5E,CAAC,CAACuG,SAAT;;;MAGFvG,CAAC,CAACuG,SAAF,GAAcf,CAAd;;GA/DuB;EAkE3BtB,aAAa,EAAE,CAlEY;EAmE3BC,cAAc,EAAE;CAnEU,EAoEzBV,QApEyB,CAA5B;;ACxYA,SAAS+C,cAAT,CAAwBvH,IAAxB,EAA8BwH,SAA9B,EAAyCC,MAAzC,EAAiD;EAC/CA,MAAM,GAAGA,MAAM,IAAI,EAAnB,CAD+C;;MAG3CD,SAAS,CAACrC,QAAV,KAAuB,CAA3B,EAA8B;IAC5BqC,SAAS,GAAGE,IAAI,CAACX,SAAL,CAAeS,SAAf,CAAZ;;;MAGEG,qBAAqB,GAAGF,MAAM,CAACE,qBAAnC;MACIC,kBAAkB,GAAGH,MAAM,CAACG,kBAAhC;MACIC,YAAY,GAAGJ,MAAM,CAACI,YAA1B;MACIC,aAAa,GAAGL,MAAM,CAACK,aAA3B;MACIC,SAAS,GAAGN,MAAM,CAACM,SAAP,IAAoB,CAApC;MACIC,UAAU,GAAGP,MAAM,CAACO,UAAP,IAAqB,CAAtC;MACIC,YAAY,GAAGR,MAAM,CAACQ,YAAP,IAAuB,CAA1C;MACIC,WAAW,GAAGT,MAAM,CAACS,WAAP,IAAsB,CAAxC;EACAP,qBAAqB,GAAGA,qBAAqB,KAAKhC,SAA1B,GAAsC,IAAtC,GAA6CgC,qBAArE;MACIQ,KAAK,GAAGT,IAAI,CAACnD,QAAL,CAAciD,SAAd,CAAZ;MACIY,UAAU,GAAGV,IAAI,CAACb,MAAL,CAAY7G,IAAZ,CAAjB;MACIqI,EAAE,GAAGX,IAAI,CAACY,WAAL,CAAiBtI,IAAjB,CAAT;MACIuI,EAAE,GAAGb,IAAI,CAACc,UAAL,CAAgBxI,IAAhB,CAAT;MACIyI,eAAJ;MACIC,EAAJ;MACIC,EAAJ;MACIC,eAAJ;MACIC,OAAJ;MACIC,UAAJ;MACIlE,GAAJ;MACImE,SAAJ;MACIC,EAAJ;MACIC,EAAJ;;MAEId,KAAJ,EAAW;IACTvD,GAAG,GAAG4C,SAAN;IACAyB,EAAE,GAAGvB,IAAI,CAACwB,MAAL,CAAYtE,GAAZ,CAAL;IACAoE,EAAE,GAAGtB,IAAI,CAACyB,KAAL,CAAWvE,GAAX,CAAL;IACAmE,SAAS,GAAG;MACVrI,IAAI,EAAEgH,IAAI,CAACN,UAAL,CAAgBxC,GAAhB,CADI;MAEVjE,GAAG,EAAE+G,IAAI,CAACJ,SAAL,CAAe1C,GAAf;KAFP,CAJS;;IASTiE,OAAO,GAAG;MACRnI,IAAI,EAAE0H,UAAU,CAAC1H,IAAX,GAAkBqI,SAAS,CAACrI,IAA5B,GAAmCsH,UADjC;MAERrH,GAAG,EAAEyH,UAAU,CAACzH,GAAX,GAAiBoI,SAAS,CAACpI,GAA3B,GAAiCoH;KAFxC;IAIAe,UAAU,GAAG;MACXpI,IAAI,EAAE0H,UAAU,CAAC1H,IAAX,GAAkB6H,EAAlB,IAAwBQ,SAAS,CAACrI,IAAV,GAAiBsI,EAAzC,IAA+Cd,WAD1C;MAEXvH,GAAG,EAAEyH,UAAU,CAACzH,GAAX,GAAiB0H,EAAjB,IAAuBU,SAAS,CAACpI,GAAV,GAAgBsI,EAAvC,IAA6ChB;KAFpD;IAIAW,eAAe,GAAGG,SAAlB;GAjBF,MAkBO;IACLN,eAAe,GAAGf,IAAI,CAACb,MAAL,CAAYW,SAAZ,CAAlB;IACAkB,EAAE,GAAGlB,SAAS,CAAC4B,YAAf;IACAT,EAAE,GAAGnB,SAAS,CAAC6B,WAAf;IACAT,eAAe,GAAG;MAChBlI,IAAI,EAAE8G,SAAS,CAACJ,UADA;MAEhBzG,GAAG,EAAE6G,SAAS,CAACF;KAFjB,CAJK;;;IAULuB,OAAO,GAAG;MACRnI,IAAI,EAAE0H,UAAU,CAAC1H,IAAX,IAAmB+H,eAAe,CAAC/H,IAAhB,IAAwB4D,UAAU,CAACoD,IAAI,CAACpB,GAAL,CAASkB,SAAT,EAAoB,iBAApB,CAAD,CAAV,IAAsD,CAA9E,CAAnB,IAAuGQ,UADrG;MAERrH,GAAG,EAAEyH,UAAU,CAACzH,GAAX,IAAkB8H,eAAe,CAAC9H,GAAhB,IAAuB2D,UAAU,CAACoD,IAAI,CAACpB,GAAL,CAASkB,SAAT,EAAoB,gBAApB,CAAD,CAAV,IAAqD,CAA5E,CAAlB,IAAoGO;KAF3G;IAIAe,UAAU,GAAG;MACXpI,IAAI,EAAE0H,UAAU,CAAC1H,IAAX,GAAkB6H,EAAlB,IAAwBE,eAAe,CAAC/H,IAAhB,GAAuBiI,EAAvB,IAA6BrE,UAAU,CAACoD,IAAI,CAACpB,GAAL,CAASkB,SAAT,EAAoB,kBAApB,CAAD,CAAV,IAAuD,CAApF,CAAxB,IAAkHU,WAD7G;MAEXvH,GAAG,EAAEyH,UAAU,CAACzH,GAAX,GAAiB0H,EAAjB,IAAuBI,eAAe,CAAC9H,GAAhB,GAAsB+H,EAAtB,IAA4BpE,UAAU,CAACoD,IAAI,CAACpB,GAAL,CAASkB,SAAT,EAAoB,mBAApB,CAAD,CAAV,IAAwD,CAApF,CAAvB,IAAiHS;KAFxH;;;MAMEY,OAAO,CAAClI,GAAR,GAAc,CAAd,IAAmBmI,UAAU,CAACnI,GAAX,GAAiB,CAAxC,EAA2C;;QAErCkH,YAAY,KAAK,IAArB,EAA2B;MACzBH,IAAI,CAACJ,SAAL,CAAeE,SAAf,EAA0BoB,eAAe,CAACjI,GAAhB,GAAsBkI,OAAO,CAAClI,GAAxD;KADF,MAEO,IAAIkH,YAAY,KAAK,KAArB,EAA4B;MACjCH,IAAI,CAACJ,SAAL,CAAeE,SAAf,EAA0BoB,eAAe,CAACjI,GAAhB,GAAsBmI,UAAU,CAACnI,GAA3D;KADK,MAEA;;UAEDkI,OAAO,CAAClI,GAAR,GAAc,CAAlB,EAAqB;QACnB+G,IAAI,CAACJ,SAAL,CAAeE,SAAf,EAA0BoB,eAAe,CAACjI,GAAhB,GAAsBkI,OAAO,CAAClI,GAAxD;OADF,MAEO;QACL+G,IAAI,CAACJ,SAAL,CAAeE,SAAf,EAA0BoB,eAAe,CAACjI,GAAhB,GAAsBmI,UAAU,CAACnI,GAA3D;;;GAXN,MAcO;QACD,CAACiH,kBAAL,EAAyB;MACvBC,YAAY,GAAGA,YAAY,KAAKlC,SAAjB,GAA6B,IAA7B,GAAoC,CAAC,CAACkC,YAArD;;UAEIA,YAAJ,EAAkB;QAChBH,IAAI,CAACJ,SAAL,CAAeE,SAAf,EAA0BoB,eAAe,CAACjI,GAAhB,GAAsBkI,OAAO,CAAClI,GAAxD;OADF,MAEO;QACL+G,IAAI,CAACJ,SAAL,CAAeE,SAAf,EAA0BoB,eAAe,CAACjI,GAAhB,GAAsBmI,UAAU,CAACnI,GAA3D;;;;;MAKFgH,qBAAJ,EAA2B;QACrBkB,OAAO,CAACnI,IAAR,GAAe,CAAf,IAAoBoI,UAAU,CAACpI,IAAX,GAAkB,CAA1C,EAA6C;;UAEvCoH,aAAa,KAAK,IAAtB,EAA4B;QAC1BJ,IAAI,CAACN,UAAL,CAAgBI,SAAhB,EAA2BoB,eAAe,CAAClI,IAAhB,GAAuBmI,OAAO,CAACnI,IAA1D;OADF,MAEO,IAAIoH,aAAa,KAAK,KAAtB,EAA6B;QAClCJ,IAAI,CAACN,UAAL,CAAgBI,SAAhB,EAA2BoB,eAAe,CAAClI,IAAhB,GAAuBoI,UAAU,CAACpI,IAA7D;OADK,MAEA;;YAEDmI,OAAO,CAACnI,IAAR,GAAe,CAAnB,EAAsB;UACpBgH,IAAI,CAACN,UAAL,CAAgBI,SAAhB,EAA2BoB,eAAe,CAAClI,IAAhB,GAAuBmI,OAAO,CAACnI,IAA1D;SADF,MAEO;UACLgH,IAAI,CAACN,UAAL,CAAgBI,SAAhB,EAA2BoB,eAAe,CAAClI,IAAhB,GAAuBoI,UAAU,CAACpI,IAA7D;;;KAXN,MAcO;UACD,CAACkH,kBAAL,EAAyB;QACvBE,aAAa,GAAGA,aAAa,KAAKnC,SAAlB,GAA8B,IAA9B,GAAqC,CAAC,CAACmC,aAAvD;;YAEIA,aAAJ,EAAmB;UACjBJ,IAAI,CAACN,UAAL,CAAgBI,SAAhB,EAA2BoB,eAAe,CAAClI,IAAhB,GAAuBmI,OAAO,CAACnI,IAA1D;SADF,MAEO;UACLgH,IAAI,CAACN,UAAL,CAAgBI,SAAhB,EAA2BoB,eAAe,CAAClI,IAAhB,GAAuBoI,UAAU,CAACpI,IAA7D;;;;;;;;;"}