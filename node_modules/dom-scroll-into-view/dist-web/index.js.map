{"version": 3, "file": "index.js", "sources": ["../../src/util.js", "../../src/index.js"], "sourcesContent": ["const RE_NUM = /[\\-+]?(?:\\d*\\.|)\\d+(?:[eE][\\-+]?\\d+|)/.source;\n\nfunction getClientPosition(elem) {\n  let box;\n  let x;\n  let y;\n  const doc = elem.ownerDocument;\n  const body = doc.body;\n  const docElem = doc && doc.documentElement;\n  // 根据 GBS 最新数据，A-Grade Browsers 都已支持 getBoundingClientRect 方法，不用再考虑传统的实现方式\n  box = elem.getBoundingClientRect();\n\n  // 注：jQuery 还考虑减去 docElem.clientLeft/clientTop\n  // 但测试发现，这样反而会导致当 html 和 body 有边距/边框样式时，获取的值不正确\n  // 此外，ie6 会忽略 html 的 margin 值，幸运地是没有谁会去设置 html 的 margin\n\n  x = box.left;\n  y = box.top;\n\n  // In IE, most of the time, 2 extra pixels are added to the top and left\n  // due to the implicit 2-pixel inset border.  In IE6/7 quirks mode and\n  // IE6 standards mode, this border can be overridden by setting the\n  // document element's border to zero -- thus, we cannot rely on the\n  // offset always being 2 pixels.\n\n  // In quirks mode, the offset can be determined by querying the body's\n  // clientLeft/clientTop, but in standards mode, it is found by querying\n  // the document element's clientLeft/clientTop.  Since we already called\n  // getClientBoundingRect we have already forced a reflow, so it is not\n  // too expensive just to query them all.\n\n  // ie 下应该减去窗口的边框吧，毕竟默认 absolute 都是相对窗口定位的\n  // 窗口边框标准是设 documentElement ,quirks 时设置 body\n  // 最好禁止在 body 和 html 上边框 ，但 ie < 9 html 默认有 2px ，减去\n  // 但是非 ie 不可能设置窗口边框，body html 也不是窗口 ,ie 可以通过 html,body 设置\n  // 标准 ie 下 docElem.clientTop 就是 border-top\n  // ie7 html 即窗口边框改变不了。永远为 2\n  // 但标准 firefox/chrome/ie9 下 docElem.clientTop 是窗口边框，即使设了 border-top 也为 0\n\n  x -= docElem.clientLeft || body.clientLeft || 0;\n  y -= docElem.clientTop || body.clientTop || 0;\n\n  return {\n    left: x,\n    top: y,\n  };\n}\n\nfunction getScroll(w, top) {\n  let ret = w[`page${top ? 'Y' : 'X'}Offset`];\n  const method = `scroll${top ? 'Top' : 'Left'}`;\n  if (typeof ret !== 'number') {\n    const d = w.document;\n    // ie6,7,8 standard mode\n    ret = d.documentElement[method];\n    if (typeof ret !== 'number') {\n      // quirks mode\n      ret = d.body[method];\n    }\n  }\n  return ret;\n}\n\nfunction getScrollLeft(w) {\n  return getScroll(w);\n}\n\nfunction getScrollTop(w) {\n  return getScroll(w, true);\n}\n\nfunction getOffset(el) {\n  const pos = getClientPosition(el);\n  const doc = el.ownerDocument;\n  const w = doc.defaultView || doc.parentWindow;\n  pos.left += getScrollLeft(w);\n  pos.top += getScrollTop(w);\n  return pos;\n}\nfunction _getComputedStyle(elem, name, computedStyle_) {\n  let val = '';\n  const d = elem.ownerDocument;\n  const computedStyle =\n    computedStyle_ || d.defaultView.getComputedStyle(elem, null);\n\n  // https://github.com/kissyteam/kissy/issues/61\n  if (computedStyle) {\n    val = computedStyle.getPropertyValue(name) || computedStyle[name];\n  }\n\n  return val;\n}\n\nconst _RE_NUM_NO_PX = new RegExp(`^(${RE_NUM})(?!px)[a-z%]+$`, 'i');\nconst RE_POS = /^(top|right|bottom|left)$/;\nconst CURRENT_STYLE = 'currentStyle';\nconst RUNTIME_STYLE = 'runtimeStyle';\nconst LEFT = 'left';\nconst PX = 'px';\n\nfunction _getComputedStyleIE(elem, name) {\n  // currentStyle maybe null\n  // http://msdn.microsoft.com/en-us/library/ms535231.aspx\n  let ret = elem[CURRENT_STYLE] && elem[CURRENT_STYLE][name];\n\n  // 当 width/height 设置为百分比时，通过 pixelLeft 方式转换的 width/height 值\n  // 一开始就处理了! CUSTOM_STYLE.height,CUSTOM_STYLE.width ,cssHook 解决@2011-08-19\n  // 在 ie 下不对，需要直接用 offset 方式\n  // borderWidth 等值也有问题，但考虑到 borderWidth 设为百分比的概率很小，这里就不考虑了\n\n  // From the awesome hack by Dean Edwards\n  // http://erik.eae.net/archives/2007/07/27/18.54.15/#comment-102291\n  // If we're not dealing with a regular pixel number\n  // but a number that has a weird ending, we need to convert it to pixels\n  // exclude left right for relativity\n  if (_RE_NUM_NO_PX.test(ret) && !RE_POS.test(name)) {\n    // Remember the original values\n    const style = elem.style;\n    const left = style[LEFT];\n    const rsLeft = elem[RUNTIME_STYLE][LEFT];\n\n    // prevent flashing of content\n    elem[RUNTIME_STYLE][LEFT] = elem[CURRENT_STYLE][LEFT];\n\n    // Put in the new values to get a computed value out\n    style[LEFT] = name === 'fontSize' ? '1em' : ret || 0;\n    ret = style.pixelLeft + PX;\n\n    // Revert the changed values\n    style[LEFT] = left;\n\n    elem[RUNTIME_STYLE][LEFT] = rsLeft;\n  }\n  return ret === '' ? 'auto' : ret;\n}\n\nlet getComputedStyleX;\nif (typeof window !== 'undefined') {\n  getComputedStyleX = window.getComputedStyle\n    ? _getComputedStyle\n    : _getComputedStyleIE;\n}\n\nfunction each(arr, fn) {\n  for (let i = 0; i < arr.length; i++) {\n    fn(arr[i]);\n  }\n}\n\nfunction isBorderBoxFn(elem) {\n  return getComputedStyleX(elem, 'boxSizing') === 'border-box';\n}\n\nconst BOX_MODELS = ['margin', 'border', 'padding'];\nconst CONTENT_INDEX = -1;\nconst PADDING_INDEX = 2;\nconst BORDER_INDEX = 1;\nconst MARGIN_INDEX = 0;\n\nfunction swap(elem, options, callback) {\n  const old = {};\n  const style = elem.style;\n  let name;\n\n  // Remember the old values, and insert the new ones\n  for (name in options) {\n    if (options.hasOwnProperty(name)) {\n      old[name] = style[name];\n      style[name] = options[name];\n    }\n  }\n\n  callback.call(elem);\n\n  // Revert the old values\n  for (name in options) {\n    if (options.hasOwnProperty(name)) {\n      style[name] = old[name];\n    }\n  }\n}\n\nfunction getPBMWidth(elem, props, which) {\n  let value = 0;\n  let prop;\n  let j;\n  let i;\n  for (j = 0; j < props.length; j++) {\n    prop = props[j];\n    if (prop) {\n      for (i = 0; i < which.length; i++) {\n        let cssProp;\n        if (prop === 'border') {\n          cssProp = `${prop + which[i]}Width`;\n        } else {\n          cssProp = prop + which[i];\n        }\n        value += parseFloat(getComputedStyleX(elem, cssProp)) || 0;\n      }\n    }\n  }\n  return value;\n}\n\n/**\n * A crude way of determining if an object is a window\n * @member util\n */\nfunction isWindow(obj) {\n  // must use == for ie8\n  /* eslint eqeqeq:0 */\n  return obj != null && obj == obj.window;\n}\n\nconst domUtils = {};\n\neach(['Width', 'Height'], name => {\n  domUtils[`doc${name}`] = refWin => {\n    const d = refWin.document;\n    return Math.max(\n      // firefox chrome documentElement.scrollHeight< body.scrollHeight\n      // ie standard mode : documentElement.scrollHeight> body.scrollHeight\n      d.documentElement[`scroll${name}`],\n      // quirks : documentElement.scrollHeight 最大等于可视窗口多一点？\n      d.body[`scroll${name}`],\n      domUtils[`viewport${name}`](d),\n    );\n  };\n\n  domUtils[`viewport${name}`] = win => {\n    // pc browser includes scrollbar in window.innerWidth\n    const prop = `client${name}`;\n    const doc = win.document;\n    const body = doc.body;\n    const documentElement = doc.documentElement;\n    const documentElementProp = documentElement[prop];\n    // 标准模式取 documentElement\n    // backcompat 取 body\n    return (\n      (doc.compatMode === 'CSS1Compat' && documentElementProp) ||\n      (body && body[prop]) ||\n      documentElementProp\n    );\n  };\n});\n\n/*\n 得到元素的大小信息\n @param elem\n @param name\n @param {String} [extra]  'padding' : (css width) + padding\n 'border' : (css width) + padding + border\n 'margin' : (css width) + padding + border + margin\n */\nfunction getWH(elem, name, extra) {\n  if (isWindow(elem)) {\n    return name === 'width'\n      ? domUtils.viewportWidth(elem)\n      : domUtils.viewportHeight(elem);\n  } else if (elem.nodeType === 9) {\n    return name === 'width'\n      ? domUtils.docWidth(elem)\n      : domUtils.docHeight(elem);\n  }\n  const which = name === 'width' ? ['Left', 'Right'] : ['Top', 'Bottom'];\n  let borderBoxValue = name === 'width' ? elem.offsetWidth : elem.offsetHeight;\n  const computedStyle = getComputedStyleX(elem);\n  const isBorderBox = isBorderBoxFn(elem, computedStyle);\n  let cssBoxValue = 0;\n  if (borderBoxValue == null || borderBoxValue <= 0) {\n    borderBoxValue = undefined;\n    // Fall back to computed then un computed css if necessary\n    cssBoxValue = getComputedStyleX(elem, name);\n    if (cssBoxValue == null || Number(cssBoxValue) < 0) {\n      cssBoxValue = elem.style[name] || 0;\n    }\n    // Normalize '', auto, and prepare for extra\n    cssBoxValue = parseFloat(cssBoxValue) || 0;\n  }\n  if (extra === undefined) {\n    extra = isBorderBox ? BORDER_INDEX : CONTENT_INDEX;\n  }\n  const borderBoxValueOrIsBorderBox =\n    borderBoxValue !== undefined || isBorderBox;\n  const val = borderBoxValue || cssBoxValue;\n  if (extra === CONTENT_INDEX) {\n    if (borderBoxValueOrIsBorderBox) {\n      return (\n        val - getPBMWidth(elem, ['border', 'padding'], which, computedStyle)\n      );\n    }\n    return cssBoxValue;\n  }\n  if (borderBoxValueOrIsBorderBox) {\n    const padding =\n      extra === PADDING_INDEX\n        ? -getPBMWidth(elem, ['border'], which, computedStyle)\n        : getPBMWidth(elem, ['margin'], which, computedStyle);\n    return val + (extra === BORDER_INDEX ? 0 : padding);\n  }\n  return (\n    cssBoxValue +\n    getPBMWidth(elem, BOX_MODELS.slice(extra), which, computedStyle)\n  );\n}\n\nconst cssShow = {\n  position: 'absolute',\n  visibility: 'hidden',\n  display: 'block',\n};\n\n// fix #119 : https://github.com/kissyteam/kissy/issues/119\nfunction getWHIgnoreDisplay(elem) {\n  let val;\n  const args = arguments;\n  // in case elem is window\n  // elem.offsetWidth === undefined\n  if (elem.offsetWidth !== 0) {\n    val = getWH.apply(undefined, args);\n  } else {\n    swap(elem, cssShow, () => {\n      val = getWH.apply(undefined, args);\n    });\n  }\n  return val;\n}\n\nfunction css(el, name, v) {\n  let value = v;\n  if (typeof name === 'object') {\n    for (const i in name) {\n      if (name.hasOwnProperty(i)) {\n        css(el, i, name[i]);\n      }\n    }\n    return undefined;\n  }\n  if (typeof value !== 'undefined') {\n    if (typeof value === 'number') {\n      value += 'px';\n    }\n    el.style[name] = value;\n    return undefined;\n  }\n  return getComputedStyleX(el, name);\n}\n\neach(['width', 'height'], name => {\n  const first = name.charAt(0).toUpperCase() + name.slice(1);\n  domUtils[`outer${first}`] = (el, includeMargin) => {\n    return (\n      el &&\n      getWHIgnoreDisplay(el, name, includeMargin ? MARGIN_INDEX : BORDER_INDEX)\n    );\n  };\n  const which = name === 'width' ? ['Left', 'Right'] : ['Top', 'Bottom'];\n\n  domUtils[name] = (elem, val) => {\n    if (val !== undefined) {\n      if (elem) {\n        const computedStyle = getComputedStyleX(elem);\n        const isBorderBox = isBorderBoxFn(elem);\n        if (isBorderBox) {\n          val += getPBMWidth(elem, ['padding', 'border'], which, computedStyle);\n        }\n        return css(elem, name, val);\n      }\n      return undefined;\n    }\n    return elem && getWHIgnoreDisplay(elem, name, CONTENT_INDEX);\n  };\n});\n\n// 设置 elem 相对 elem.ownerDocument 的坐标\nfunction setOffset(elem, offset) {\n  // set position first, in-case top/left are set even on static elem\n  if (css(elem, 'position') === 'static') {\n    elem.style.position = 'relative';\n  }\n\n  const old = getOffset(elem);\n  const ret = {};\n  let current;\n  let key;\n\n  for (key in offset) {\n    if (offset.hasOwnProperty(key)) {\n      current = parseFloat(css(elem, key)) || 0;\n      ret[key] = current + offset[key] - old[key];\n    }\n  }\n  css(elem, ret);\n}\n\nexport default {\n  getWindow(node) {\n    const doc = node.ownerDocument || node;\n    return doc.defaultView || doc.parentWindow;\n  },\n  offset(el, value) {\n    if (typeof value !== 'undefined') {\n      setOffset(el, value);\n    } else {\n      return getOffset(el);\n    }\n  },\n  isWindow,\n  each,\n  css,\n  clone(obj) {\n    const ret = {};\n    for (const i in obj) {\n      if (obj.hasOwnProperty(i)) {\n        ret[i] = obj[i];\n      }\n    }\n    const overflow = obj.overflow;\n    if (overflow) {\n      for (const i in obj) {\n        if (obj.hasOwnProperty(i)) {\n          ret.overflow[i] = obj.overflow[i];\n        }\n      }\n    }\n    return ret;\n  },\n  scrollLeft(w, v) {\n    if (isWindow(w)) {\n      if (v === undefined) {\n        return getScrollLeft(w);\n      }\n      window.scrollTo(v, getScrollTop(w));\n    } else {\n      if (v === undefined) {\n        return w.scrollLeft;\n      }\n      w.scrollLeft = v;\n    }\n  },\n  scrollTop(w, v) {\n    if (isWindow(w)) {\n      if (v === undefined) {\n        return getScrollTop(w);\n      }\n      window.scrollTo(getScrollLeft(w), v);\n    } else {\n      if (v === undefined) {\n        return w.scrollTop;\n      }\n      w.scrollTop = v;\n    }\n  },\n  viewportWidth: 0,\n  viewportHeight: 0,\n  ...domUtils,\n};\n", "import util from './util';\n\nfunction scrollIntoView(elem, container, config) {\n  config = config || {};\n  // document 归一化到 window\n  if (container.nodeType === 9) {\n    container = util.getWindow(container);\n  }\n\n  let allowHorizontalScroll = config.allowHorizontalScroll;\n  const onlyScrollIfNeeded = config.onlyScrollIfNeeded;\n  let alignWithTop = config.alignWithTop;\n  let alignWithLeft = config.alignWithLeft;\n  const offsetTop = config.offsetTop || 0;\n  const offsetLeft = config.offsetLeft || 0;\n  const offsetBottom = config.offsetBottom || 0;\n  const offsetRight = config.offsetRight || 0;\n\n  allowHorizontalScroll =\n    allowHorizontalScroll === undefined ? true : allowHorizontalScroll;\n\n  const isWin = util.isWindow(container);\n  const elemOffset = util.offset(elem);\n  const eh = util.outerHeight(elem);\n  const ew = util.outerWidth(elem);\n  let containerOffset;\n  let ch;\n  let cw;\n  let containerScroll;\n  let diffTop;\n  let diffBottom;\n  let win;\n  let winScroll;\n  let ww;\n  let wh;\n\n  if (isWin) {\n    win = container;\n    wh = util.height(win);\n    ww = util.width(win);\n    winScroll = {\n      left: util.scrollLeft(win),\n      top: util.scrollTop(win),\n    };\n    // elem 相对 container 可视视窗的距离\n    diffTop = {\n      left: elemOffset.left - winScroll.left - offsetLeft,\n      top: elemOffset.top - winScroll.top - offsetTop,\n    };\n    diffBottom = {\n      left: elemOffset.left + ew - (winScroll.left + ww) + offsetRight,\n      top: elemOffset.top + eh - (winScroll.top + wh) + offsetBottom,\n    };\n    containerScroll = winScroll;\n  } else {\n    containerOffset = util.offset(container);\n    ch = container.clientHeight;\n    cw = container.clientWidth;\n    containerScroll = {\n      left: container.scrollLeft,\n      top: container.scrollTop,\n    };\n    // elem 相对 container 可视视窗的距离\n    // 注意边框, offset 是边框到根节点\n    diffTop = {\n      left:\n        elemOffset.left -\n        (containerOffset.left +\n          (parseFloat(util.css(container, 'borderLeftWidth')) || 0)) -\n        offsetLeft,\n      top:\n        elemOffset.top -\n        (containerOffset.top +\n          (parseFloat(util.css(container, 'borderTopWidth')) || 0)) -\n        offsetTop,\n    };\n    diffBottom = {\n      left:\n        elemOffset.left +\n        ew -\n        (containerOffset.left +\n          cw +\n          (parseFloat(util.css(container, 'borderRightWidth')) || 0)) +\n        offsetRight,\n      top:\n        elemOffset.top +\n        eh -\n        (containerOffset.top +\n          ch +\n          (parseFloat(util.css(container, 'borderBottomWidth')) || 0)) +\n        offsetBottom,\n    };\n  }\n\n  if (diffTop.top < 0 || diffBottom.top > 0) {\n    // 强制向上\n    if (alignWithTop === true) {\n      util.scrollTop(container, containerScroll.top + diffTop.top);\n    } else if (alignWithTop === false) {\n      util.scrollTop(container, containerScroll.top + diffBottom.top);\n    } else {\n      // 自动调整\n      if (diffTop.top < 0) {\n        util.scrollTop(container, containerScroll.top + diffTop.top);\n      } else {\n        util.scrollTop(container, containerScroll.top + diffBottom.top);\n      }\n    }\n  } else {\n    if (!onlyScrollIfNeeded) {\n      alignWithTop = alignWithTop === undefined ? true : !!alignWithTop;\n      if (alignWithTop) {\n        util.scrollTop(container, containerScroll.top + diffTop.top);\n      } else {\n        util.scrollTop(container, containerScroll.top + diffBottom.top);\n      }\n    }\n  }\n\n  if (allowHorizontalScroll) {\n    if (diffTop.left < 0 || diffBottom.left > 0) {\n      // 强制向上\n      if (alignWithLeft === true) {\n        util.scrollLeft(container, containerScroll.left + diffTop.left);\n      } else if (alignWithLeft === false) {\n        util.scrollLeft(container, containerScroll.left + diffBottom.left);\n      } else {\n        // 自动调整\n        if (diffTop.left < 0) {\n          util.scrollLeft(container, containerScroll.left + diffTop.left);\n        } else {\n          util.scrollLeft(container, containerScroll.left + diffBottom.left);\n        }\n      }\n    } else {\n      if (!onlyScrollIfNeeded) {\n        alignWithLeft = alignWithLeft === undefined ? true : !!alignWithLeft;\n        if (alignWithLeft) {\n          util.scrollLeft(container, containerScroll.left + diffTop.left);\n        } else {\n          util.scrollLeft(container, containerScroll.left + diffBottom.left);\n        }\n      }\n    }\n  }\n}\n\nexport default scrollIntoView;\n"], "names": ["RE_NUM", "source", "getClientPosition", "elem", "box", "x", "y", "doc", "ownerDocument", "body", "doc<PERSON><PERSON>", "documentElement", "getBoundingClientRect", "left", "top", "clientLeft", "clientTop", "getScroll", "w", "ret", "method", "d", "document", "getScrollLeft", "getScrollTop", "getOffset", "el", "pos", "defaultView", "parentWindow", "_getComputedStyle", "name", "computedStyle_", "val", "computedStyle", "getComputedStyle", "getPropertyValue", "_RE_NUM_NO_PX", "RegExp", "RE_POS", "CURRENT_STYLE", "RUNTIME_STYLE", "LEFT", "PX", "_getComputedStyleIE", "test", "style", "rsLeft", "pixelLeft", "getComputedStyleX", "window", "each", "arr", "fn", "i", "length", "isBorderBoxFn", "BOX_MODELS", "CONTENT_INDEX", "PADDING_INDEX", "BORDER_INDEX", "MARGIN_INDEX", "swap", "options", "callback", "old", "hasOwnProperty", "call", "getPBMWidth", "props", "which", "value", "prop", "j", "cssProp", "parseFloat", "isWindow", "obj", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "refWin", "Math", "max", "win", "documentElementProp", "compatMode", "getWH", "extra", "viewportWidth", "viewportHeight", "nodeType", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "doc<PERSON><PERSON>ght", "borderBoxValue", "offsetWidth", "offsetHeight", "isBorderBox", "cssBoxValue", "undefined", "Number", "borderBoxValueOrIsBorderBox", "padding", "slice", "cssShow", "position", "visibility", "display", "getWHIgnoreDisplay", "args", "arguments", "apply", "css", "v", "first", "char<PERSON>t", "toUpperCase", "<PERSON><PERSON><PERSON><PERSON>", "setOffset", "offset", "current", "key", "getWindow", "node", "clone", "overflow", "scrollLeft", "scrollTo", "scrollTop", "scrollIntoView", "container", "config", "util", "allowHorizontalScroll", "onlyScrollIfNeeded", "alignWithTop", "alignWithLeft", "offsetTop", "offsetLeft", "offsetBottom", "offsetRight", "isWin", "elemOffset", "eh", "outerHeight", "ew", "outerWidth", "containerOffset", "ch", "cw", "containerScroll", "diffTop", "diffBottom", "winScroll", "ww", "wh", "height", "width", "clientHeight", "clientWidth"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAMA,MAAM,GAAG,wCAAwCC,MAAvD;;AAEA,SAASC,iBAAT,CAA2BC,IAA3B,EAAiC;MAC3BC,GAAJ;MACIC,CAAJ;MACIC,CAAJ;MACMC,GAAG,GAAGJ,IAAI,CAACK,aAAjB;MACMC,IAAI,GAAGF,GAAG,CAACE,IAAjB;MACMC,OAAO,GAAGH,GAAG,IAAIA,GAAG,CAACI,eAA3B,CAN+B;;EAQ/BP,GAAG,GAAGD,IAAI,CAACS,qBAAL,EAAN,CAR+B;;;;EAc/BP,CAAC,GAAGD,GAAG,CAACS,IAAR;EACAP,CAAC,GAAGF,GAAG,CAACU,GAAR,CAf+B;;;;;;;;;;;;;;;;;;EAqC/BT,CAAC,IAAIK,OAAO,CAACK,UAAR,IAAsBN,IAAI,CAACM,UAA3B,IAAyC,CAA9C;EACAT,CAAC,IAAII,OAAO,CAACM,SAAR,IAAqBP,IAAI,CAACO,SAA1B,IAAuC,CAA5C;SAEO;IACLH,IAAI,EAAER,CADD;IAELS,GAAG,EAAER;GAFP;;;AAMF,SAASW,SAAT,CAAmBC,CAAnB,EAAsBJ,GAAtB,EAA2B;MACrBK,GAAG,GAAGD,CAAC,eAAQJ,GAAG,GAAG,GAAH,GAAS,GAApB,YAAX;MACMM,MAAM,mBAAYN,GAAG,GAAG,KAAH,GAAW,MAA1B,CAAZ;;MACI,OAAOK,GAAP,KAAe,QAAnB,EAA6B;QACrBE,CAAC,GAAGH,CAAC,CAACI,QAAZ,CAD2B;;IAG3BH,GAAG,GAAGE,CAAC,CAACV,eAAF,CAAkBS,MAAlB,CAAN;;QACI,OAAOD,GAAP,KAAe,QAAnB,EAA6B;;MAE3BA,GAAG,GAAGE,CAAC,CAACZ,IAAF,CAAOW,MAAP,CAAN;;;;SAGGD,GAAP;;;AAGF,SAASI,aAAT,CAAuBL,CAAvB,EAA0B;SACjBD,SAAS,CAACC,CAAD,CAAhB;;;AAGF,SAASM,YAAT,CAAsBN,CAAtB,EAAyB;SAChBD,SAAS,CAACC,CAAD,EAAI,IAAJ,CAAhB;;;AAGF,SAASO,SAAT,CAAmBC,EAAnB,EAAuB;MACfC,GAAG,GAAGzB,iBAAiB,CAACwB,EAAD,CAA7B;MACMnB,GAAG,GAAGmB,EAAE,CAAClB,aAAf;MACMU,CAAC,GAAGX,GAAG,CAACqB,WAAJ,IAAmBrB,GAAG,CAACsB,YAAjC;EACAF,GAAG,CAACd,IAAJ,IAAYU,aAAa,CAACL,CAAD,CAAzB;EACAS,GAAG,CAACb,GAAJ,IAAWU,YAAY,CAACN,CAAD,CAAvB;SACOS,GAAP;;;AAEF,SAASG,iBAAT,CAA2B3B,IAA3B,EAAiC4B,IAAjC,EAAuCC,cAAvC,EAAuD;MACjDC,GAAG,GAAG,EAAV;MACMZ,CAAC,GAAGlB,IAAI,CAACK,aAAf;MACM0B,aAAa,GACjBF,cAAc,IAAIX,CAAC,CAACO,WAAF,CAAcO,gBAAd,CAA+BhC,IAA/B,EAAqC,IAArC,CADpB,CAHqD;;MAOjD+B,aAAJ,EAAmB;IACjBD,GAAG,GAAGC,aAAa,CAACE,gBAAd,CAA+BL,IAA/B,KAAwCG,aAAa,CAACH,IAAD,CAA3D;;;SAGKE,GAAP;;;AAGF,IAAMI,aAAa,GAAG,IAAIC,MAAJ,aAAgBtC,MAAhB,sBAAyC,GAAzC,CAAtB;;AACA,IAAMuC,MAAM,GAAG,2BAAf;AACA,IAAMC,aAAa,GAAG,cAAtB;AACA,IAAMC,aAAa,GAAG,cAAtB;AACA,IAAMC,IAAI,GAAG,MAAb;AACA,IAAMC,EAAE,GAAG,IAAX;;AAEA,SAASC,mBAAT,CAA6BzC,IAA7B,EAAmC4B,IAAnC,EAAyC;;;MAGnCZ,GAAG,GAAGhB,IAAI,CAACqC,aAAD,CAAJ,IAAuBrC,IAAI,CAACqC,aAAD,CAAJ,CAAoBT,IAApB,CAAjC,CAHuC;;;;;;;;;;MAenCM,aAAa,CAACQ,IAAd,CAAmB1B,GAAnB,KAA2B,CAACoB,MAAM,CAACM,IAAP,CAAYd,IAAZ,CAAhC,EAAmD;;QAE3Ce,KAAK,GAAG3C,IAAI,CAAC2C,KAAnB;QACMjC,IAAI,GAAGiC,KAAK,CAACJ,IAAD,CAAlB;QACMK,MAAM,GAAG5C,IAAI,CAACsC,aAAD,CAAJ,CAAoBC,IAApB,CAAf,CAJiD;;IAOjDvC,IAAI,CAACsC,aAAD,CAAJ,CAAoBC,IAApB,IAA4BvC,IAAI,CAACqC,aAAD,CAAJ,CAAoBE,IAApB,CAA5B,CAPiD;;IAUjDI,KAAK,CAACJ,IAAD,CAAL,GAAcX,IAAI,KAAK,UAAT,GAAsB,KAAtB,GAA8BZ,GAAG,IAAI,CAAnD;IACAA,GAAG,GAAG2B,KAAK,CAACE,SAAN,GAAkBL,EAAxB,CAXiD;;IAcjDG,KAAK,CAACJ,IAAD,CAAL,GAAc7B,IAAd;IAEAV,IAAI,CAACsC,aAAD,CAAJ,CAAoBC,IAApB,IAA4BK,MAA5B;;;SAEK5B,GAAG,KAAK,EAAR,GAAa,MAAb,GAAsBA,GAA7B;;;AAGF,IAAI8B,iBAAJ;;AACA,IAAI,OAAOC,MAAP,KAAkB,WAAtB,EAAmC;EACjCD,iBAAiB,GAAGC,MAAM,CAACf,gBAAP,GAChBL,iBADgB,GAEhBc,mBAFJ;;;AAKF,SAASO,IAAT,CAAcC,GAAd,EAAmBC,EAAnB,EAAuB;OAChB,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,GAAG,CAACG,MAAxB,EAAgCD,CAAC,EAAjC,EAAqC;IACnCD,EAAE,CAACD,GAAG,CAACE,CAAD,CAAJ,CAAF;;;;AAIJ,SAASE,aAAT,CAAuBrD,IAAvB,EAA6B;SACpB8C,iBAAiB,CAAC9C,IAAD,EAAO,WAAP,CAAjB,KAAyC,YAAhD;;;AAGF,IAAMsD,UAAU,GAAG,CAAC,QAAD,EAAW,QAAX,EAAqB,SAArB,CAAnB;AACA,IAAMC,aAAa,GAAG,CAAC,CAAvB;AACA,IAAMC,aAAa,GAAG,CAAtB;AACA,IAAMC,YAAY,GAAG,CAArB;AACA,IAAMC,YAAY,GAAG,CAArB;;AAEA,SAASC,IAAT,CAAc3D,IAAd,EAAoB4D,OAApB,EAA6BC,QAA7B,EAAuC;MAC/BC,GAAG,GAAG,EAAZ;MACMnB,KAAK,GAAG3C,IAAI,CAAC2C,KAAnB;MACIf,IAAJ,CAHqC;;OAMhCA,IAAL,IAAagC,OAAb,EAAsB;QAChBA,OAAO,CAACG,cAAR,CAAuBnC,IAAvB,CAAJ,EAAkC;MAChCkC,GAAG,CAAClC,IAAD,CAAH,GAAYe,KAAK,CAACf,IAAD,CAAjB;MACAe,KAAK,CAACf,IAAD,CAAL,GAAcgC,OAAO,CAAChC,IAAD,CAArB;;;;EAIJiC,QAAQ,CAACG,IAAT,CAAchE,IAAd,EAbqC;;OAgBhC4B,IAAL,IAAagC,OAAb,EAAsB;QAChBA,OAAO,CAACG,cAAR,CAAuBnC,IAAvB,CAAJ,EAAkC;MAChCe,KAAK,CAACf,IAAD,CAAL,GAAckC,GAAG,CAAClC,IAAD,CAAjB;;;;;AAKN,SAASqC,WAAT,CAAqBjE,IAArB,EAA2BkE,KAA3B,EAAkCC,KAAlC,EAAyC;MACnCC,KAAK,GAAG,CAAZ;MACIC,IAAJ;MACIC,CAAJ;MACInB,CAAJ;;OACKmB,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGJ,KAAK,CAACd,MAAtB,EAA8BkB,CAAC,EAA/B,EAAmC;IACjCD,IAAI,GAAGH,KAAK,CAACI,CAAD,CAAZ;;QACID,IAAJ,EAAU;WACHlB,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGgB,KAAK,CAACf,MAAtB,EAA8BD,CAAC,EAA/B,EAAmC;YAC7BoB,OAAO,SAAX;;YACIF,IAAI,KAAK,QAAb,EAAuB;UACrBE,OAAO,aAAMF,IAAI,GAAGF,KAAK,CAAChB,CAAD,CAAlB,UAAP;SADF,MAEO;UACLoB,OAAO,GAAGF,IAAI,GAAGF,KAAK,CAAChB,CAAD,CAAtB;;;QAEFiB,KAAK,IAAII,UAAU,CAAC1B,iBAAiB,CAAC9C,IAAD,EAAOuE,OAAP,CAAlB,CAAV,IAAgD,CAAzD;;;;;SAICH,KAAP;;;;;;;;AAOF,SAASK,QAAT,CAAkBC,GAAlB,EAAuB;;;;SAGdA,GAAG,IAAI,IAAP,IAAeA,GAAG,IAAIA,GAAG,CAAC3B,MAAjC;;;AAGF,IAAM4B,QAAQ,GAAG,EAAjB;AAEA3B,IAAI,CAAC,CAAC,OAAD,EAAU,QAAV,CAAD,EAAsB,UAAApB,IAAI,EAAI;EAChC+C,QAAQ,cAAO/C,IAAP,EAAR,GAAyB,UAAAgD,MAAM,EAAI;QAC3B1D,CAAC,GAAG0D,MAAM,CAACzD,QAAjB;WACO0D,IAAI,CAACC,GAAL;;IAGL5D,CAAC,CAACV,eAAF,iBAA2BoB,IAA3B,EAHK;IAKLV,CAAC,CAACZ,IAAF,iBAAgBsB,IAAhB,EALK,EAML+C,QAAQ,mBAAY/C,IAAZ,EAAR,CAA4BV,CAA5B,CANK,CAAP;GAFF;;EAYAyD,QAAQ,mBAAY/C,IAAZ,EAAR,GAA8B,UAAAmD,GAAG,EAAI;;QAE7BV,IAAI,mBAAYzC,IAAZ,CAAV;QACMxB,GAAG,GAAG2E,GAAG,CAAC5D,QAAhB;QACMb,IAAI,GAAGF,GAAG,CAACE,IAAjB;QACME,eAAe,GAAGJ,GAAG,CAACI,eAA5B;QACMwE,mBAAmB,GAAGxE,eAAe,CAAC6D,IAAD,CAA3C,CANmC;;;WAUhCjE,GAAG,CAAC6E,UAAJ,KAAmB,YAAnB,IAAmCD,mBAApC,IACC1E,IAAI,IAAIA,IAAI,CAAC+D,IAAD,CADb,IAEAW,mBAHF;GATF;CAbE,CAAJ;;;;;;;;;;AAsCA,SAASE,KAAT,CAAelF,IAAf,EAAqB4B,IAArB,EAA2BuD,KAA3B,EAAkC;MAC5BV,QAAQ,CAACzE,IAAD,CAAZ,EAAoB;WACX4B,IAAI,KAAK,OAAT,GACH+C,QAAQ,CAACS,aAAT,CAAuBpF,IAAvB,CADG,GAEH2E,QAAQ,CAACU,cAAT,CAAwBrF,IAAxB,CAFJ;GADF,MAIO,IAAIA,IAAI,CAACsF,QAAL,KAAkB,CAAtB,EAAyB;WACvB1D,IAAI,KAAK,OAAT,GACH+C,QAAQ,CAACY,QAAT,CAAkBvF,IAAlB,CADG,GAEH2E,QAAQ,CAACa,SAAT,CAAmBxF,IAAnB,CAFJ;;;MAIImE,KAAK,GAAGvC,IAAI,KAAK,OAAT,GAAmB,CAAC,MAAD,EAAS,OAAT,CAAnB,GAAuC,CAAC,KAAD,EAAQ,QAAR,CAArD;MACI6D,cAAc,GAAG7D,IAAI,KAAK,OAAT,GAAmB5B,IAAI,CAAC0F,WAAxB,GAAsC1F,IAAI,CAAC2F,YAAhE;MACM5D,aAAa,GAAGe,iBAAiB,CAAC9C,IAAD,CAAvC;MACM4F,WAAW,GAAGvC,aAAa,CAACrD,IAAD,AAAA,CAAjC;MACI6F,WAAW,GAAG,CAAlB;;MACIJ,cAAc,IAAI,IAAlB,IAA0BA,cAAc,IAAI,CAAhD,EAAmD;IACjDA,cAAc,GAAGK,SAAjB,CADiD;;IAGjDD,WAAW,GAAG/C,iBAAiB,CAAC9C,IAAD,EAAO4B,IAAP,CAA/B;;QACIiE,WAAW,IAAI,IAAf,IAAuBE,MAAM,CAACF,WAAD,CAAN,GAAsB,CAAjD,EAAoD;MAClDA,WAAW,GAAG7F,IAAI,CAAC2C,KAAL,CAAWf,IAAX,KAAoB,CAAlC;KAL+C;;;IAQjDiE,WAAW,GAAGrB,UAAU,CAACqB,WAAD,CAAV,IAA2B,CAAzC;;;MAEEV,KAAK,KAAKW,SAAd,EAAyB;IACvBX,KAAK,GAAGS,WAAW,GAAGnC,YAAH,GAAkBF,aAArC;;;MAEIyC,2BAA2B,GAC/BP,cAAc,KAAKK,SAAnB,IAAgCF,WADlC;MAEM9D,GAAG,GAAG2D,cAAc,IAAII,WAA9B;;MACIV,KAAK,KAAK5B,aAAd,EAA6B;QACvByC,2BAAJ,EAAiC;aAE7BlE,GAAG,GAAGmC,WAAW,CAACjE,IAAD,EAAO,CAAC,QAAD,EAAW,SAAX,CAAP,EAA8BmE,KAA9B,AAAA,CADnB;;;WAIK0B,WAAP;;;MAEEG,2BAAJ,EAAiC;QACzBC,OAAO,GACXd,KAAK,KAAK3B,aAAV,GACI,CAACS,WAAW,CAACjE,IAAD,EAAO,CAAC,QAAD,CAAP,EAAmBmE,KAAnB,AAAA,CADhB,GAEIF,WAAW,CAACjE,IAAD,EAAO,CAAC,QAAD,CAAP,EAAmBmE,KAAnB,AAAA,CAHjB;WAIOrC,GAAG,IAAIqD,KAAK,KAAK1B,YAAV,GAAyB,CAAzB,GAA6BwC,OAAjC,CAAV;;;SAGAJ,WAAW,GACX5B,WAAW,CAACjE,IAAD,EAAOsD,UAAU,CAAC4C,KAAX,CAAiBf,KAAjB,CAAP,EAAgChB,KAAhC,AAAA,CAFb;;;AAMF,IAAMgC,OAAO,GAAG;EACdC,QAAQ,EAAE,UADI;EAEdC,UAAU,EAAE,QAFE;EAGdC,OAAO,EAAE;CAHX;;AAOA,SAASC,kBAAT,CAA4BvG,IAA5B,EAAkC;MAC5B8B,GAAJ;MACM0E,IAAI,GAAGC,SAAb,CAFgC;;;MAK5BzG,IAAI,CAAC0F,WAAL,KAAqB,CAAzB,EAA4B;IAC1B5D,GAAG,GAAGoD,KAAK,CAACwB,KAAN,CAAYZ,SAAZ,EAAuBU,IAAvB,CAAN;GADF,MAEO;IACL7C,IAAI,CAAC3D,IAAD,EAAOmG,OAAP,EAAgB,YAAM;MACxBrE,GAAG,GAAGoD,KAAK,CAACwB,KAAN,CAAYZ,SAAZ,EAAuBU,IAAvB,CAAN;KADE,CAAJ;;;SAIK1E,GAAP;;;AAGF,SAAS6E,GAAT,CAAapF,EAAb,EAAiBK,IAAjB,EAAuBgF,CAAvB,EAA0B;MACpBxC,KAAK,GAAGwC,CAAZ;;MACI,QAAOhF,IAAP,MAAgB,QAApB,EAA8B;SACvB,IAAMuB,CAAX,IAAgBvB,IAAhB,EAAsB;UAChBA,IAAI,CAACmC,cAAL,CAAoBZ,CAApB,CAAJ,EAA4B;QAC1BwD,GAAG,CAACpF,EAAD,EAAK4B,CAAL,EAAQvB,IAAI,CAACuB,CAAD,CAAZ,CAAH;;;;WAGG2C,SAAP;;;MAEE,OAAO1B,KAAP,KAAiB,WAArB,EAAkC;QAC5B,OAAOA,KAAP,KAAiB,QAArB,EAA+B;MAC7BA,KAAK,IAAI,IAAT;;;IAEF7C,EAAE,CAACoB,KAAH,CAASf,IAAT,IAAiBwC,KAAjB;WACO0B,SAAP;;;SAEKhD,iBAAiB,CAACvB,EAAD,EAAKK,IAAL,CAAxB;;;AAGFoB,IAAI,CAAC,CAAC,OAAD,EAAU,QAAV,CAAD,EAAsB,UAAApB,IAAI,EAAI;MAC1BiF,KAAK,GAAGjF,IAAI,CAACkF,MAAL,CAAY,CAAZ,EAAeC,WAAf,KAA+BnF,IAAI,CAACsE,KAAL,CAAW,CAAX,CAA7C;;EACAvB,QAAQ,gBAASkC,KAAT,EAAR,GAA4B,UAACtF,EAAD,EAAKyF,aAAL,EAAuB;WAE/CzF,EAAE,IACFgF,kBAAkB,CAAChF,EAAD,EAAKK,IAAL,EAAWoF,aAAa,GAAGtD,YAAH,GAAkBD,YAA1C,CAFpB;GADF;;MAMMU,KAAK,GAAGvC,IAAI,KAAK,OAAT,GAAmB,CAAC,MAAD,EAAS,OAAT,CAAnB,GAAuC,CAAC,KAAD,EAAQ,QAAR,CAArD;;EAEA+C,QAAQ,CAAC/C,IAAD,CAAR,GAAiB,UAAC5B,IAAD,EAAO8B,GAAP,EAAe;QAC1BA,GAAG,KAAKgE,SAAZ,EAAuB;UACjB9F,IAAJ,EAAU;YACF+B,aAAa,GAAGe,iBAAiB,CAAC9C,IAAD,CAAvC;YACM4F,WAAW,GAAGvC,aAAa,CAACrD,IAAD,CAAjC;;YACI4F,WAAJ,EAAiB;UACf9D,GAAG,IAAImC,WAAW,CAACjE,IAAD,EAAO,CAAC,SAAD,EAAY,QAAZ,CAAP,EAA8BmE,KAA9B,AAAA,CAAlB;;;eAEKwC,GAAG,CAAC3G,IAAD,EAAO4B,IAAP,EAAaE,GAAb,CAAV;;;aAEKgE,SAAP;;;WAEK9F,IAAI,IAAIuG,kBAAkB,CAACvG,IAAD,EAAO4B,IAAP,EAAa2B,aAAb,CAAjC;GAZF;CAVE,CAAJ;;AA2BA,SAAS0D,SAAT,CAAmBjH,IAAnB,EAAyBkH,MAAzB,EAAiC;;MAE3BP,GAAG,CAAC3G,IAAD,EAAO,UAAP,CAAH,KAA0B,QAA9B,EAAwC;IACtCA,IAAI,CAAC2C,KAAL,CAAWyD,QAAX,GAAsB,UAAtB;;;MAGItC,GAAG,GAAGxC,SAAS,CAACtB,IAAD,CAArB;MACMgB,GAAG,GAAG,EAAZ;MACImG,OAAJ;MACIC,GAAJ;;OAEKA,GAAL,IAAYF,MAAZ,EAAoB;QACdA,MAAM,CAACnD,cAAP,CAAsBqD,GAAtB,CAAJ,EAAgC;MAC9BD,OAAO,GAAG3C,UAAU,CAACmC,GAAG,CAAC3G,IAAD,EAAOoH,GAAP,CAAJ,CAAV,IAA8B,CAAxC;MACApG,GAAG,CAACoG,GAAD,CAAH,GAAWD,OAAO,GAAGD,MAAM,CAACE,GAAD,CAAhB,GAAwBtD,GAAG,CAACsD,GAAD,CAAtC;;;;EAGJT,GAAG,CAAC3G,IAAD,EAAOgB,GAAP,CAAH;;;AAGF;EACEqG,SADF,qBACYC,IADZ,EACkB;QACRlH,GAAG,GAAGkH,IAAI,CAACjH,aAAL,IAAsBiH,IAAlC;WACOlH,GAAG,CAACqB,WAAJ,IAAmBrB,GAAG,CAACsB,YAA9B;GAHJ;EAKEwF,MALF,kBAKS3F,EALT,EAKa6C,KALb,EAKoB;QACZ,OAAOA,KAAP,KAAiB,WAArB,EAAkC;MAChC6C,SAAS,CAAC1F,EAAD,EAAK6C,KAAL,CAAT;KADF,MAEO;aACE9C,SAAS,CAACC,EAAD,CAAhB;;GATN;EAYEkD,QAAQ,EAARA,QAZF;EAaEzB,IAAI,EAAJA,IAbF;EAcE2D,GAAG,EAAHA,GAdF;EAeEY,KAfF,iBAeQ7C,GAfR,EAea;QACH1D,GAAG,GAAG,EAAZ;;SACK,IAAMmC,CAAX,IAAgBuB,GAAhB,EAAqB;UACfA,GAAG,CAACX,cAAJ,CAAmBZ,CAAnB,CAAJ,EAA2B;QACzBnC,GAAG,CAACmC,CAAD,CAAH,GAASuB,GAAG,CAACvB,CAAD,CAAZ;;;;QAGEqE,QAAQ,GAAG9C,GAAG,CAAC8C,QAArB;;QACIA,QAAJ,EAAc;WACP,IAAMrE,EAAX,IAAgBuB,GAAhB,EAAqB;YACfA,GAAG,CAACX,cAAJ,CAAmBZ,EAAnB,CAAJ,EAA2B;UACzBnC,GAAG,CAACwG,QAAJ,CAAarE,EAAb,IAAkBuB,GAAG,CAAC8C,QAAJ,CAAarE,EAAb,CAAlB;;;;;WAICnC,GAAP;GA9BJ;EAgCEyG,UAhCF,sBAgCa1G,CAhCb,EAgCgB6F,CAhChB,EAgCmB;QACXnC,QAAQ,CAAC1D,CAAD,CAAZ,EAAiB;UACX6F,CAAC,KAAKd,SAAV,EAAqB;eACZ1E,aAAa,CAACL,CAAD,CAApB;;;MAEFgC,MAAM,CAAC2E,QAAP,CAAgBd,CAAhB,EAAmBvF,YAAY,CAACN,CAAD,CAA/B;KAJF,MAKO;UACD6F,CAAC,KAAKd,SAAV,EAAqB;eACZ/E,CAAC,CAAC0G,UAAT;;;MAEF1G,CAAC,CAAC0G,UAAF,GAAeb,CAAf;;GA1CN;EA6CEe,SA7CF,qBA6CY5G,CA7CZ,EA6Ce6F,CA7Cf,EA6CkB;QACVnC,QAAQ,CAAC1D,CAAD,CAAZ,EAAiB;UACX6F,CAAC,KAAKd,SAAV,EAAqB;eACZzE,YAAY,CAACN,CAAD,CAAnB;;;MAEFgC,MAAM,CAAC2E,QAAP,CAAgBtG,aAAa,CAACL,CAAD,CAA7B,EAAkC6F,CAAlC;KAJF,MAKO;UACDA,CAAC,KAAKd,SAAV,EAAqB;eACZ/E,CAAC,CAAC4G,SAAT;;;MAEF5G,CAAC,CAAC4G,SAAF,GAAcf,CAAd;;GAvDN;EA0DExB,aAAa,EAAE,CA1DjB;EA2DEC,cAAc,EAAE;GACbV,QA5DL;;ACzYA,SAASiD,cAAT,CAAwB5H,IAAxB,EAA8B6H,SAA9B,EAAyCC,MAAzC,EAAiD;EAC/CA,MAAM,GAAGA,MAAM,IAAI,EAAnB,CAD+C;;MAG3CD,SAAS,CAACvC,QAAV,KAAuB,CAA3B,EAA8B;IAC5BuC,SAAS,GAAGE,IAAI,CAACV,SAAL,CAAeQ,SAAf,CAAZ;;;MAGEG,qBAAqB,GAAGF,MAAM,CAACE,qBAAnC;MACMC,kBAAkB,GAAGH,MAAM,CAACG,kBAAlC;MACIC,YAAY,GAAGJ,MAAM,CAACI,YAA1B;MACIC,aAAa,GAAGL,MAAM,CAACK,aAA3B;MACMC,SAAS,GAAGN,MAAM,CAACM,SAAP,IAAoB,CAAtC;MACMC,UAAU,GAAGP,MAAM,CAACO,UAAP,IAAqB,CAAxC;MACMC,YAAY,GAAGR,MAAM,CAACQ,YAAP,IAAuB,CAA5C;MACMC,WAAW,GAAGT,MAAM,CAACS,WAAP,IAAsB,CAA1C;EAEAP,qBAAqB,GACnBA,qBAAqB,KAAKlC,SAA1B,GAAsC,IAAtC,GAA6CkC,qBAD/C;MAGMQ,KAAK,GAAGT,IAAI,CAACtD,QAAL,CAAcoD,SAAd,CAAd;MACMY,UAAU,GAAGV,IAAI,CAACb,MAAL,CAAYlH,IAAZ,CAAnB;MACM0I,EAAE,GAAGX,IAAI,CAACY,WAAL,CAAiB3I,IAAjB,CAAX;MACM4I,EAAE,GAAGb,IAAI,CAACc,UAAL,CAAgB7I,IAAhB,CAAX;MACI8I,eAAJ;MACIC,EAAJ;MACIC,EAAJ;MACIC,eAAJ;MACIC,OAAJ;MACIC,UAAJ;MACIpE,GAAJ;MACIqE,SAAJ;MACIC,EAAJ;MACIC,EAAJ;;MAEId,KAAJ,EAAW;IACTzD,GAAG,GAAG8C,SAAN;IACAyB,EAAE,GAAGvB,IAAI,CAACwB,MAAL,CAAYxE,GAAZ,CAAL;IACAsE,EAAE,GAAGtB,IAAI,CAACyB,KAAL,CAAWzE,GAAX,CAAL;IACAqE,SAAS,GAAG;MACV1I,IAAI,EAAEqH,IAAI,CAACN,UAAL,CAAgB1C,GAAhB,CADI;MAEVpE,GAAG,EAAEoH,IAAI,CAACJ,SAAL,CAAe5C,GAAf;KAFP,CAJS;;IASTmE,OAAO,GAAG;MACRxI,IAAI,EAAE+H,UAAU,CAAC/H,IAAX,GAAkB0I,SAAS,CAAC1I,IAA5B,GAAmC2H,UADjC;MAER1H,GAAG,EAAE8H,UAAU,CAAC9H,GAAX,GAAiByI,SAAS,CAACzI,GAA3B,GAAiCyH;KAFxC;IAIAe,UAAU,GAAG;MACXzI,IAAI,EAAE+H,UAAU,CAAC/H,IAAX,GAAkBkI,EAAlB,IAAwBQ,SAAS,CAAC1I,IAAV,GAAiB2I,EAAzC,IAA+Cd,WAD1C;MAEX5H,GAAG,EAAE8H,UAAU,CAAC9H,GAAX,GAAiB+H,EAAjB,IAAuBU,SAAS,CAACzI,GAAV,GAAgB2I,EAAvC,IAA6ChB;KAFpD;IAIAW,eAAe,GAAGG,SAAlB;GAjBF,MAkBO;IACLN,eAAe,GAAGf,IAAI,CAACb,MAAL,CAAYW,SAAZ,CAAlB;IACAkB,EAAE,GAAGlB,SAAS,CAAC4B,YAAf;IACAT,EAAE,GAAGnB,SAAS,CAAC6B,WAAf;IACAT,eAAe,GAAG;MAChBvI,IAAI,EAAEmH,SAAS,CAACJ,UADA;MAEhB9G,GAAG,EAAEkH,SAAS,CAACF;KAFjB,CAJK;;;IAULuB,OAAO,GAAG;MACRxI,IAAI,EACF+H,UAAU,CAAC/H,IAAX,IACCoI,eAAe,CAACpI,IAAhB,IACE8D,UAAU,CAACuD,IAAI,CAACpB,GAAL,CAASkB,SAAT,EAAoB,iBAApB,CAAD,CAAV,IAAsD,CADxD,CADD,IAGAQ,UALM;MAMR1H,GAAG,EACD8H,UAAU,CAAC9H,GAAX,IACCmI,eAAe,CAACnI,GAAhB,IACE6D,UAAU,CAACuD,IAAI,CAACpB,GAAL,CAASkB,SAAT,EAAoB,gBAApB,CAAD,CAAV,IAAqD,CADvD,CADD,IAGAO;KAVJ;IAYAe,UAAU,GAAG;MACXzI,IAAI,EACF+H,UAAU,CAAC/H,IAAX,GACAkI,EADA,IAECE,eAAe,CAACpI,IAAhB,GACCsI,EADD,IAEExE,UAAU,CAACuD,IAAI,CAACpB,GAAL,CAASkB,SAAT,EAAoB,kBAApB,CAAD,CAAV,IAAuD,CAFzD,CAFD,IAKAU,WAPS;MAQX5H,GAAG,EACD8H,UAAU,CAAC9H,GAAX,GACA+H,EADA,IAECI,eAAe,CAACnI,GAAhB,GACCoI,EADD,IAEEvE,UAAU,CAACuD,IAAI,CAACpB,GAAL,CAASkB,SAAT,EAAoB,mBAApB,CAAD,CAAV,IAAwD,CAF1D,CAFD,IAKAS;KAdJ;;;MAkBEY,OAAO,CAACvI,GAAR,GAAc,CAAd,IAAmBwI,UAAU,CAACxI,GAAX,GAAiB,CAAxC,EAA2C;;QAErCuH,YAAY,KAAK,IAArB,EAA2B;MACzBH,IAAI,CAACJ,SAAL,CAAeE,SAAf,EAA0BoB,eAAe,CAACtI,GAAhB,GAAsBuI,OAAO,CAACvI,GAAxD;KADF,MAEO,IAAIuH,YAAY,KAAK,KAArB,EAA4B;MACjCH,IAAI,CAACJ,SAAL,CAAeE,SAAf,EAA0BoB,eAAe,CAACtI,GAAhB,GAAsBwI,UAAU,CAACxI,GAA3D;KADK,MAEA;;UAEDuI,OAAO,CAACvI,GAAR,GAAc,CAAlB,EAAqB;QACnBoH,IAAI,CAACJ,SAAL,CAAeE,SAAf,EAA0BoB,eAAe,CAACtI,GAAhB,GAAsBuI,OAAO,CAACvI,GAAxD;OADF,MAEO;QACLoH,IAAI,CAACJ,SAAL,CAAeE,SAAf,EAA0BoB,eAAe,CAACtI,GAAhB,GAAsBwI,UAAU,CAACxI,GAA3D;;;GAXN,MAcO;QACD,CAACsH,kBAAL,EAAyB;MACvBC,YAAY,GAAGA,YAAY,KAAKpC,SAAjB,GAA6B,IAA7B,GAAoC,CAAC,CAACoC,YAArD;;UACIA,YAAJ,EAAkB;QAChBH,IAAI,CAACJ,SAAL,CAAeE,SAAf,EAA0BoB,eAAe,CAACtI,GAAhB,GAAsBuI,OAAO,CAACvI,GAAxD;OADF,MAEO;QACLoH,IAAI,CAACJ,SAAL,CAAeE,SAAf,EAA0BoB,eAAe,CAACtI,GAAhB,GAAsBwI,UAAU,CAACxI,GAA3D;;;;;MAKFqH,qBAAJ,EAA2B;QACrBkB,OAAO,CAACxI,IAAR,GAAe,CAAf,IAAoByI,UAAU,CAACzI,IAAX,GAAkB,CAA1C,EAA6C;;UAEvCyH,aAAa,KAAK,IAAtB,EAA4B;QAC1BJ,IAAI,CAACN,UAAL,CAAgBI,SAAhB,EAA2BoB,eAAe,CAACvI,IAAhB,GAAuBwI,OAAO,CAACxI,IAA1D;OADF,MAEO,IAAIyH,aAAa,KAAK,KAAtB,EAA6B;QAClCJ,IAAI,CAACN,UAAL,CAAgBI,SAAhB,EAA2BoB,eAAe,CAACvI,IAAhB,GAAuByI,UAAU,CAACzI,IAA7D;OADK,MAEA;;YAEDwI,OAAO,CAACxI,IAAR,GAAe,CAAnB,EAAsB;UACpBqH,IAAI,CAACN,UAAL,CAAgBI,SAAhB,EAA2BoB,eAAe,CAACvI,IAAhB,GAAuBwI,OAAO,CAACxI,IAA1D;SADF,MAEO;UACLqH,IAAI,CAACN,UAAL,CAAgBI,SAAhB,EAA2BoB,eAAe,CAACvI,IAAhB,GAAuByI,UAAU,CAACzI,IAA7D;;;KAXN,MAcO;UACD,CAACuH,kBAAL,EAAyB;QACvBE,aAAa,GAAGA,aAAa,KAAKrC,SAAlB,GAA8B,IAA9B,GAAqC,CAAC,CAACqC,aAAvD;;YACIA,aAAJ,EAAmB;UACjBJ,IAAI,CAACN,UAAL,CAAgBI,SAAhB,EAA2BoB,eAAe,CAACvI,IAAhB,GAAuBwI,OAAO,CAACxI,IAA1D;SADF,MAEO;UACLqH,IAAI,CAACN,UAAL,CAAgBI,SAAhB,EAA2BoB,eAAe,CAACvI,IAAhB,GAAuByI,UAAU,CAACzI,IAA7D;;;;;;;;;"}