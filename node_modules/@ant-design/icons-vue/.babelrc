{"env": {"test": {"presets": [["@babel/preset-env", {"targets": {"node": "current"}}]], "plugins": [["@vue/babel-plugin-jsx", {"mergeProps": false, "enableObjectSlots": false}], "@babel/plugin-transform-member-expression-literals", "@babel/plugin-transform-object-assign", "@babel/plugin-transform-property-literals", "@babel/plugin-transform-spread", "@babel/plugin-transform-template-literals", "@babel/plugin-proposal-export-default-from", "@babel/plugin-proposal-export-namespace-from", "@babel/plugin-proposal-object-rest-spread", "@babel/plugin-proposal-class-properties"]}}}