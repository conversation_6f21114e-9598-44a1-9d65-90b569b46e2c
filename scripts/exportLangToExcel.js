const xlsx = require('xlsx');
const fs = require('fs');
const path = require('path');

// 语言文件目录
const LANG_DIR = path.join(__dirname, './lang');
// 基准语言代码
const BASE_LANG = 'zh_CN';

// 扁平化对象
const flattenObject = (obj, prefix = '') => {
    let result = {};
    
    for (const key in obj) {
        if (typeof obj[key] === 'object' && obj[key] !== null) {
            const flatObject = flattenObject(obj[key], `${prefix}${key}.`);
            result = { ...result, ...flatObject };
        } else {
            result[`${prefix}${key}`] = obj[key];
        }
    }
    
    return result;
};

// 主函数
const exportToExcel = () => {
    // 获取所有语言文件
    const langFiles = fs.readdirSync(LANG_DIR)
        .filter(file => file.endsWith('.json'))
        .map(file => ({
            code: path.basename(file, '.json'),
            path: path.join(LANG_DIR, file)
        }));
    
    console.log(`Found ${langFiles.length} language files:`, langFiles.map(f => f.code).join(', '));
    
    if (langFiles.length === 0) {
        console.error('No language files found!');
        return;
    }
    
    // 读取并扁平化所有语言文件
    const langData = {};
    const allKeys = new Set();
    
    langFiles.forEach(langFile => {
        try {
            const content = JSON.parse(fs.readFileSync(langFile.path, 'utf8'));
            const flatContent = flattenObject(content);
            
            langData[langFile.code] = flatContent;
            
            // 收集所有键
            Object.keys(flatContent).forEach(key => allKeys.add(key));
        } catch (error) {
            console.error(`Error processing ${langFile.path}:`, error.message);
        }
    });
    
    // 对语言代码进行排序，确保中文在第二位
    const sortedLangCodes = langFiles.map(f => f.code).sort((a, b) => {
        if (a === BASE_LANG) return -1;
        if (b === BASE_LANG) return 1;
        return a.localeCompare(b);
    });
    
    // 创建表头
    const headers = ['key', ...sortedLangCodes];
    
    // 创建数据行
    const rows = [headers]; // 表头
    
    // 按键排序并添加数据
    Array.from(allKeys).sort().forEach(key => {
        const row = [key];
        
        // 按排序后的语言代码添加每种语言的翻译
        sortedLangCodes.forEach(langCode => {
            row.push(langData[langCode]?.[key] || '');
        });
        
        rows.push(row);
    });
    
    // 创建工作簿和工作表
    const wb = xlsx.utils.book_new();
    const ws = xlsx.utils.aoa_to_sheet(rows);
    
    // 设置列宽
    ws['!cols'] = [
        { wch: 50 },  // key列
        ...sortedLangCodes.map(() => ({ wch: 50 }))  // 每种语言的列
    ];
    
    // 添加工作表到工作簿
    xlsx.utils.book_append_sheet(wb, ws, 'Translations');
    
    // 保存Excel文件
    const outputFile = 'lang_translations.xlsx';
    xlsx.writeFile(wb, outputFile);
    console.log(`Excel file has been created successfully: ${outputFile}`);
};

exportToExcel();
