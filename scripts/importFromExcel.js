const xlsx = require('xlsx');
const fs = require('fs');
const path = require('path');
const ts = require('typescript');

// 修改导入目录为新的位置
const I18N_DIR = path.join(__dirname, '../public/i18n');
const IMPORT_DIR = path.join(__dirname, '../public/i18n_new');

// 将扁平化的对象转换回嵌套对象
const unflattenObject = (obj) => {
    const result = {};
    
    for (const key in obj) {
        const keys = key.split('.');
        let current = result;
        
        for (let i = 0; i < keys.length; i++) {
            const k = keys[i];
            if (i === keys.length - 1) {
                current[k] = obj[key];
            } else {
                current[k] = current[k] || {};
                current = current[k];
            }
        }
    }
    
    return result;
};

// 确保目录存在
const ensureDirectoryExists = (dirPath) => {
    if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
    }
};

// 生成 TypeScript 文件内容
const generateTypeScriptContent = (obj) => {
    return `export default ${JSON.stringify(obj, null, 2)};\n`;
};

// 格式化 TypeScript 代码
const formatTypeScriptCode = (code) => {
    return ts.createPrinter().printFile(
        ts.createSourceFile(
            'temp.ts',
            code,
            ts.ScriptTarget.Latest,
            true,
            ts.ScriptKind.TS
        )
    );
};

// 比较两个目录的内容
const compareDirectories = (sourceDir, targetDir) => {
    const differences = {
        added: [],
        modified: [],
        removed: []
    };

    const walkDir = (dir, relativePath = '') => {
        const files = fs.readdirSync(dir);
        
        files.forEach(file => {
            const sourcePath = path.join(dir, file);
            const targetPath = path.join(targetDir, relativePath, file);
            const relPath = path.join(relativePath, file);
            
            if (fs.statSync(sourcePath).isDirectory()) {
                if (!fs.existsSync(targetPath)) {
                    differences.added.push(relPath + '/');
                }
                walkDir(sourcePath, relPath);
            } else if (file.endsWith('.ts')) {
                if (!fs.existsSync(targetPath)) {
                    differences.added.push(relPath);
                } else {
                    const sourceContent = fs.readFileSync(sourcePath, 'utf8');
                    const targetContent = fs.readFileSync(targetPath, 'utf8');
                    if (sourceContent !== targetContent) {
                        differences.modified.push(relPath);
                    }
                }
            }
        });
    };

    walkDir(sourceDir);
    return differences;
};

// 主函数
const importFromExcel = () => {
    // 创建新的导入目录
    if (fs.existsSync(IMPORT_DIR)) {
        fs.rmSync(IMPORT_DIR, { recursive: true });
    }
    fs.mkdirSync(IMPORT_DIR);

    // 读取Excel文件，添加编码选项
    const workbook = xlsx.readFile('translations.xlsx', {
        codepage: 65001, // UTF-8
        cellStyles: true,
        cellNF: false,
        cellDates: true
    });
    
    // 处理每个 sheet
    workbook.SheetNames.forEach(sheetName => {
        console.log(`Processing sheet: ${sheetName}`);
        const worksheet = workbook.Sheets[sheetName];
        
        // 获取工作表的范围
        const range = xlsx.utils.decode_range(worksheet['!ref']);
        
        // 获取所有列名（第一行）
        const headers = [];
        for (let C = 0; C <= range.e.c; C++) {
            const cellAddress = xlsx.utils.encode_cell({r: 0, c: C});
            const cell = worksheet[cellAddress];
            if (cell && cell.v) {
                headers[C] = String(cell.v).trim();
            }
        }
        
        console.log('Headers:', headers);

        // 按语言分组数据
        const languageData = {};
        
        // 从第二行开始处理数据
        for (let R = 1; R <= range.e.r; R++) {
            const keyCell = worksheet[xlsx.utils.encode_cell({r: R, c: 1})];
            const key = keyCell?.v;
            
            if (!key || typeof key !== 'string') continue; // 跳过没有key的行

            // 从第三列开始是语言数据（跳过描述列和key列）
            for (let C = 2; C <= range.e.c; C++) {
                const lang = headers[C];
                if (!lang || lang === '文件描述') continue;

                const cell = worksheet[xlsx.utils.encode_cell({r: R, c: C})];
                if (cell && cell.v) {
                    languageData[lang] = languageData[lang] || {};
                    languageData[lang][key] = String(cell.v).trim();
                }
            }
        }

        // 检查数据
        console.log('Processed data structure:', Object.keys(languageData));

        // 将数据写入新目录
        Object.keys(languageData).forEach(lang => {
            let basePath = path.join(IMPORT_DIR, lang);
            // 如果不是 root sheet，则创建对应的子文件夹
            if (sheetName !== 'root') {
                basePath = path.join(basePath, sheetName);
            }
            ensureDirectoryExists(basePath);
            
            // 按文件名分组
            const fileGroups = {};
            Object.keys(languageData[lang]).forEach(key => {
                const [fileName, ...rest] = key.split('.');
                const restKey = rest.join('.');
                fileGroups[fileName] = fileGroups[fileName] || {};
                fileGroups[fileName][restKey] = languageData[lang][key];
            });
            
            // 写入各个文件
            Object.keys(fileGroups).forEach(fileName => {
                const content = unflattenObject(fileGroups[fileName]);
                
                // 生成并格式化 TypeScript 文件内容
                let tsContent = generateTypeScriptContent(content);
                tsContent = formatTypeScriptCode(tsContent);
                
                // 写入文件
                const tsFilePath = path.join(basePath, `${fileName}.ts`);
                fs.writeFileSync(tsFilePath, tsContent, 'utf8');
            });
        });
    });

    // 比较新旧目录的差异
    const languages = fs.readdirSync(IMPORT_DIR);
    languages.forEach(lang => {
        const oldLangDir = path.join(I18N_DIR, lang);
        const newLangDir = path.join(IMPORT_DIR, lang);
        if (fs.existsSync(oldLangDir)) {
            const differences = compareDirectories(newLangDir, oldLangDir);
            console.log(`\n差异报告 (${lang}):`);
            if (differences.added.length > 0) {
                console.log('新增:', differences.added);
            }
            if (differences.modified.length > 0) {
                console.log('修改:', differences.modified);
            }
        } else {
            console.log(`\n新增语言: ${lang}`);
        }
    });
    
    console.log('\n导入完成！新的翻译文件已生成在:', IMPORT_DIR);
    console.log('请检查差异报告，确认无误后手动合并到原目录。');
};

importFromExcel();
