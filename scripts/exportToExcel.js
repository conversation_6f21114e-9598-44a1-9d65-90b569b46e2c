const xlsx = require('xlsx');
const fs = require('fs');
const path = require('path');
const ts = require('typescript');
const fileDescriptions = require('./i18nFileDescriptions');

const I18N_DIR = path.join(__dirname, '../public/i18n');

// 解析 TS 文件内容
const parseTsContent = (filePath) => {
    const sourceFile = ts.createSourceFile(
        filePath,
        fs.readFileSync(filePath, 'utf8'),
        ts.ScriptTarget.Latest,
        true
    );

    let exportedObject = {};
    const printer = ts.createPrinter({ removeComments: true });

    // 直接从 statements 中查找 export default 语句
    const exportDefaultStatement = sourceFile.statements.find(
        statement => ts.isExportAssignment(statement) && !statement.isExportEquals
    );

    if (exportDefaultStatement && ts.isExportAssignment(exportDefaultStatement)) {
        const printed = printer.printNode(
            ts.EmitHint.Expression,
            exportDefaultStatement.expression,
            sourceFile
        );
        exportedObject = Function('return ' + printed)();
    }

    return exportedObject;
};

// 获取所有语言目录
const getLangDirs = () => {
    return fs.readdirSync(I18N_DIR)
        .filter(file => fs.statSync(path.join(I18N_DIR, file)).isDirectory());
};

// 递归读取文件夹内容，返回按文件夹分组的数据
const readFiles = (dir) => {
    const result = {};
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory()) {
            // 如果是文件夹，创建新的 sheet
            result[file] = readFilesInDir(filePath);
        }
    });
    
    // 处理根目录下的文件
    const rootFiles = readFilesInDir(dir, files.filter(f => !fs.statSync(path.join(dir, f)).isDirectory()));
    if (Object.keys(rootFiles).length > 0) {
        result['root'] = rootFiles;
    }
    
    return result;
};

// 读取指定目录下的文件
const readFilesInDir = (dir, specificFiles = null) => {
    let result = {};
    const files = specificFiles || fs.readdirSync(dir).filter(f => !fs.statSync(path.join(dir, f)).isDirectory());
    
    for (const file of files) {
        if (file.endsWith('.json') || file.endsWith('.ts')) {
            const filePath = path.join(dir, file);
            const fileName = file.replace(/\.(json|ts)$/, '');
            let fileContent;
            
            if (file.endsWith('.json')) {
                fileContent = JSON.parse(fs.readFileSync(filePath, 'utf8'));
            } else {
                fileContent = parseTsContent(filePath);
            }
            
            const flattenedContent = flattenObject(fileContent, `${fileName}.`);
            result = { ...result, ...flattenedContent };
        }
    }
    
    return result;
};

// 扁平化对象
const flattenObject = (obj, prefix = '') => {
    let result = {};
    
    for (const key in obj) {
        if (typeof obj[key] === 'object' && obj[key] !== null) {
            const flatObject = flattenObject(obj[key], `${prefix}${key}.`);
            result = { ...result, ...flatObject };
        } else {
            result[`${prefix}${key}`] = obj[key];
        }
    }
    
    return result;
};

// 主函数
const exportToExcel = () => {
    const langDirs = getLangDirs();
    // 确保中文在第二列
    const sortedLangDirs = langDirs.sort((a, b) => {
        if (a === 'zh-CN') return -1;
        if (b === 'zh-CN') return 1;
        return a.localeCompare(b);
    });
    
    // 创建工作簿
    const wb = xlsx.utils.book_new();
    
    // 收集所有语言的数据，按文件夹分组
    const allData = {};
    sortedLangDirs.forEach(lang => {
        const langPath = path.join(I18N_DIR, lang);
        allData[lang] = readFiles(langPath);
    });
    
    // 为每个文件夹创建一个 sheet
    Object.keys(allData[sortedLangDirs[0]]).forEach(folder => {
        const allKeys = new Set();
        sortedLangDirs.forEach(lang => {
            if (allData[lang][folder]) {
                Object.keys(allData[lang][folder]).forEach(key => allKeys.add(key));
            }
        });
        
        // 按文件名分组
        const fileGroups = {};
        Array.from(allKeys).forEach(key => {
            const fileName = key.split('.')[0];
            if (!fileGroups[fileName]) {
                fileGroups[fileName] = [];
            }
            fileGroups[fileName].push(key);
        });

        // 转换为Excel格式
        const rows = [];
        // 添加表头
        rows.push(['文件描述', 'key', ...sortedLangDirs]);

        // 按文件名排序并添加数据
        Object.keys(fileGroups).sort().forEach(fileName => {
            const keys = fileGroups[fileName].sort();
            keys.forEach((key, index) => {
                const row = [
                    // 只在每个文件的第一行显示描述
                    index === 0 ? (fileDescriptions[fileName] || fileName) : '',
                    key
                ];
                sortedLangDirs.forEach(lang => {
                    row.push(allData[lang][folder]?.[key] || '');
                });
                rows.push(row);
            });
        });

        // 创建工作表
        const ws = xlsx.utils.aoa_to_sheet(rows);

        // 设置合并单元格
        ws['!merges'] = [];
        let currentFile = '';
        let startRow = 1;
        
        for (let i = 1; i < rows.length; i++) {
            const fileDesc = rows[i][0];
            if (fileDesc) {
                if (currentFile && startRow < i) {
                    ws['!merges'].push({
                        s: { r: startRow, c: 0 },
                        e: { r: i - 1, c: 0 }
                    });
                }
                currentFile = fileDesc;
                startRow = i;
            }
        }
        // 处理最后一个文件的合并
        if (currentFile && startRow < rows.length - 1) {
            ws['!merges'].push({
                s: { r: startRow, c: 0 },
                e: { r: rows.length - 1, c: 0 }
            });
        }

        // 设置列宽
        ws['!cols'] = [
            { wch: 20 },  // 文件描述列
            { wch: 50 },  // key列
            ...sortedLangDirs.map(() => ({ wch: 50 }))  // 语言列
        ];
        
        // 添加工作表到工作簿
        xlsx.utils.book_append_sheet(wb, ws, folder);
    });
    
    // 保存Excel文件
    xlsx.writeFile(wb, 'translations.xlsx');
    console.log('Excel file has been created successfully!');
};

exportToExcel();
