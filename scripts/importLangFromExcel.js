const xlsx = require('xlsx');
const fs = require('fs');
const path = require('path');

// 语言文件目录
const LANG_DIR = path.join(__dirname, './lang');
// 备份目录
const BACKUP_DIR = path.join(__dirname, './lang_backup');

// 将扁平化的对象转换回嵌套对象
const unflattenObject = (obj) => {
    const result = {};
    
    for (const key in obj) {
        const keys = key.split('.');
        let current = result;
        
        for (let i = 0; i < keys.length; i++) {
            const k = keys[i];
            if (i === keys.length - 1) {
                current[k] = obj[key];
            } else {
                current[k] = current[k] || {};
                current = current[k];
            }
        }
    }
    
    return result;
};

// 扁平化对象
const flattenObject = (obj, prefix = '') => {
    let result = {};
    
    for (const key in obj) {
        if (typeof obj[key] === 'object' && obj[key] !== null) {
            const flatObject = flattenObject(obj[key], `${prefix}${key}.`);
            result = { ...result, ...flatObject };
        } else {
            result[`${prefix}${key}`] = obj[key];
        }
    }
    
    return result;
};

// 确保目录存在
const ensureDirectoryExists = (dirPath) => {
    if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
    }
};

// 备份文件
const backupFile = (filePath) => {
    if (!fs.existsSync(filePath)) return false;
    
    const fileName = path.basename(filePath);
    const backupPath = path.join(BACKUP_DIR, fileName);
    
    ensureDirectoryExists(BACKUP_DIR);
    fs.copyFileSync(filePath, backupPath);
    
    return true;
};

// 比较两个对象，返回差异
const compareObjects = (oldObj, newObj) => {
    const flatOld = flattenObject(oldObj);
    const flatNew = flattenObject(newObj);
    
    const added = [];
    const modified = [];
    const removed = [];
    
    // 检查新增和修改的键
    Object.keys(flatNew).forEach(key => {
        if (!(key in flatOld)) {
            added.push(key);
        } else if (flatOld[key] !== flatNew[key]) {
            modified.push(key);
        }
    });
    
    // 检查删除的键
    Object.keys(flatOld).forEach(key => {
        if (!(key in flatNew)) {
            removed.push(key);
        }
    });
    
    return { added, modified, removed };
};

// 主函数
const importLangFromExcel = () => {
    console.log('Starting import process...');
    
    // 确保语言目录存在
    ensureDirectoryExists(LANG_DIR);
    
    // 读取Excel文件
    let workbook;
    try {
        workbook = xlsx.readFile('lang_translations.xlsx', {
            codepage: 65001, // UTF-8
            cellStyles: true,
            cellNF: false,
            cellDates: true
        });
    } catch (error) {
        console.error('Error reading Excel file:', error.message);
        return;
    }
    
    if (!workbook.SheetNames.includes('Translations')) {
        console.error('Error: Sheet "Translations" not found in the Excel file.');
        return;
    }
    
    const worksheet = workbook.Sheets['Translations'];
    
    // 将工作表转换为数组
    const data = xlsx.utils.sheet_to_json(worksheet, { header: 1 });
    
    if (data.length < 2) {
        console.error('Error: Excel file does not contain enough data.');
        return;
    }
    
    // 获取表头（第一行）
    const headers = data[0];
    
    if (headers.length < 2 || headers[0] !== 'key') {
        console.error('Error: Invalid Excel format. First column should be "key".');
        return;
    }
    
    // 按语言分组数据
    const languageData = {};
    
    // 初始化每种语言的数据对象
    for (let i = 1; i < headers.length; i++) {
        languageData[headers[i]] = {};
    }
    
    // 处理每一行数据
    for (let i = 1; i < data.length; i++) {
        const row = data[i];
        const key = row[0];
        
        if (!key) continue; // 跳过没有key的行
        
        // 处理每种语言的翻译
        for (let j = 1; j < headers.length; j++) {
            const langCode = headers[j];
            const value = row[j];
            
            if (value !== undefined && value !== null && value !== '') {
                languageData[langCode][key] = value;
            }
        }
    }
    
    // 读取现有文件，比较差异，然后更新
    Object.keys(languageData).forEach(langCode => {
        const filePath = path.join(LANG_DIR, `${langCode}.json`);
        const newData = unflattenObject(languageData[langCode]);
        let oldData = {};
        let differences = { added: [], modified: [], removed: [] };
        
        // 如果文件存在，读取并比较差异
        if (fs.existsSync(filePath)) {
            try {
                oldData = JSON.parse(fs.readFileSync(filePath, 'utf8'));
                differences = compareObjects(oldData, newData);
                
                // 备份文件
                if (backupFile(filePath)) {
                    console.log(`Backed up: ${filePath}`);
                }
            } catch (error) {
                console.error(`Error reading existing file ${filePath}:`, error.message);
            }
        } else {
            // 新文件，所有键都是新增的
            differences.added = Object.keys(flattenObject(newData));
        }
        
        // 写入新文件
        try {
            fs.writeFileSync(
                filePath,
                JSON.stringify(newData, null, 4),
                'utf8'
            );
            
            // 输出差异报告
            console.log(`\nUpdated: ${filePath}`);
            if (differences.added.length > 0) {
                console.log(`  Added: ${differences.added.length} keys`);
                console.log(`  Added keys: ${differences.added.slice(0, 5).join(', ')}${differences.added.length > 5 ? '...' : ''}`);
            }
            if (differences.modified.length > 0) {
                console.log(`  Modified: ${differences.modified.length} keys`);
                console.log(`  Modified keys: ${differences.modified.slice(0, 5).join(', ')}${differences.modified.length > 5 ? '...' : ''}`);
            }
            if (differences.removed.length > 0) {
                console.log(`  Removed: ${differences.removed.length} keys`);
                console.log(`  Removed keys: ${differences.removed.slice(0, 5).join(', ')}${differences.removed.length > 5 ? '...' : ''}`);
            }
            
            // 如果没有差异
            if (differences.added.length === 0 && 
                differences.modified.length === 0 && 
                differences.removed.length === 0) {
                console.log('  No changes');
            }
        } catch (error) {
            console.error(`Error writing file ${filePath}:`, error.message);
        }
    });
    
    console.log('\nImport completed successfully!');
    console.log(`Backup files are stored in: ${BACKUP_DIR}`);
};

// 执行导入
importLangFromExcel();