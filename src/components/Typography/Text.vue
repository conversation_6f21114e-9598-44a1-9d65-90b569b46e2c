<template>
  <component
    :is="tag"
    :class="textClasses"
    :style="textStyles"
    @click="handleClick"
    v-bind="$attrs"
  >
    <!-- 可编辑状态 -->
    <template v-if="isEditing">
      <textarea
        ref="editInput"
        v-model="editValue"
        :maxlength="editableConfig?.maxLength"
        :style="textareaStyle"
        @blur="handleEditBlur"
        @keydown="handleEditKeydown"
        @input="handleEditInput"
      />
      <span v-if="editableConfig?.enterIcon" class="ant-typography-edit-enter" @click="handleEditConfirm">
        {{ editableConfig.enterIcon }}
      </span>
    </template>
    
    <!-- 正常显示状态 -->
    <template v-else>
      <!-- 省略功能的文本内容 -->
      <template v-if="ellipsisConfig">
        <!-- 隐藏的测量元素，用于省略检测 -->
        <span ref="textContent" style="position: absolute; visibility: hidden; white-space: nowrap;">
          {{ originalText }}
        </span>

        <template v-if="isEllipsis && !isExpanded">
          <a-tooltip v-if="shouldShowTooltip" :title="tooltipTitle" placement="top">
            <span class="ant-typography-ellipsis-content" :style="textStyles">{{ displayText }}</span>
          </a-tooltip>
          <span v-else class="ant-typography-ellipsis-content" :style="textStyles">{{ displayText }}</span>
          <button
            v-if="ellipsisConfig.expandable"
            class="ant-typography-expand"
            @click="handleExpand"
          >
            {{ expandText }}
          </button>
        </template>
        <template v-else>
          <span :style="textStyles">{{ displayText }}</span>
          <button
            v-if="ellipsisConfig.expandable && hasEllipsis"
            class="ant-typography-expand"
            @click="handleExpand"
          >
            {{ expandText }}
          </button>
        </template>
      </template>

      <!-- 普通文本内容 -->
      <span v-else ref="textContent">
        <slot>{{ text || displayText }}</slot>
      </span>
      
      <!-- 复制按钮 -->
      <button
        v-if="copyableConfig"
        class="ant-typography-copy"
        :title="copyTooltip"
        @click="handleCopy"
      >
        <span v-if="copyableConfig.icon">{{ copyIcon }}</span>
        <span v-else>📋</span>
      </button>
      
      <!-- 编辑按钮 -->
      <button
        v-if="editableConfig && !isEditing && editableConfig.triggerType?.includes('icon')"
        class="ant-typography-edit"
        :title="editableConfig.tooltip"
        @click="handleEditStart"
      >
        <span v-if="editableConfig.icon">{{ editableConfig.icon }}</span>
        <span v-else>✏️</span>
      </button>
    </template>
  </component>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUnmounted, watch } from 'vue'
import { Tooltip } from 'ant-design-vue'
import type { Ref, ComputedRef } from 'vue'

// 类型定义
interface CopyableConfig {
  /** 复制的文本内容，如果不指定则使用组件的文本内容 */
  text?: string | (() => string | Promise<string>)
  /** 复制成功的回调函数 */
  onCopy?: (event: Event | null, options: { text: string }) => void
  /** 复制按钮的图标，可以是字符串或数组 [默认图标, 复制成功图标] */
  icon?: string | [string, string]
  /** 提示文本 [默认提示, 复制成功提示] */
  tooltips?: [string, string]
  /** 复制的格式类型 */
  format?: 'text/plain' | 'text/html'
}

interface EditableConfig {
  /** 编辑按钮的图标 */
  icon?: string
  /** 编辑按钮的提示文本 */
  tooltip?: string
  /** 是否处于编辑状态 */
  editing?: boolean
  /** 最大输入长度 */
  maxLength?: number
  /** 是否自动调整大小 */
  autoSize?: boolean
  /** 编辑的文本内容 */
  text?: string
  /** 文本变化时的回调 */
  onChange?: ((value: string) => void) | null
  /** 取消编辑时的回调 */
  onCancel?: (() => void) | null
  /** 开始编辑时的回调 */
  onStart?: (() => void) | null
  /** 结束编辑时的回调 */
  onEnd?: ((value: string) => void) | null
  /** 触发编辑的方式 */
  triggerType?: ('icon' | 'text')[]
  /** 确认编辑的图标 */
  enterIcon?: string
}

interface EllipsisConfig {
  /** 显示的行数，1 表示单行省略 */
  rows: number
  /** 是否可以展开 */
  expandable: boolean
  /** 省略后缀 */
  suffix: string
  /** 展开/收起的文本或函数 */
  symbol: string | ((expanded: boolean) => string)
  /** 是否显示 tooltip，可以是布尔值或自定义文本 */
  tooltip: boolean | string
  /** 展开/收起时的回调 */
  onExpand?: ((event: Event | null, options: { expanded: boolean }) => void) | null
  /** 省略状态变化时的回调 */
  onEllipsis?: ((ellipsis: boolean) => void) | null
}

// Props 定义
interface Props {
  /** 是否为代码样式 */
  code?: boolean
  /** 是否为删除线样式 */
  delete?: boolean
  /** 是否禁用 */
  disabled?: boolean
  /** 是否为键盘样式 */
  keyboard?: boolean
  /** 是否为标记样式 */
  mark?: boolean
  /** 是否为粗体样式 */
  strong?: boolean
  /** 是否为斜体样式 */
  italic?: boolean
  /** 是否为下划线样式 */
  underline?: boolean
  /** 复制功能配置，true 表示启用默认配置，对象表示自定义配置 */
  copyable?: boolean | CopyableConfig
  /** 编辑功能配置，true 表示启用默认配置，对象表示自定义配置 */
  editable?: boolean | EditableConfig
  /** 省略功能配置，true 表示启用默认配置，对象表示自定义配置 */
  ellipsis?: boolean | EllipsisConfig
  /** 文本类型，影响颜色显示 */
  type?: 'secondary' | 'success' | 'warning' | 'danger'
  /** 文本内容 */
  text?: string
}

// 默认值
const props = withDefaults(defineProps<Props>(), {
  code: false,
  delete: false,
  disabled: false,
  keyboard: false,
  mark: false,
  strong: false,
  italic: false,
  underline: false,
  copyable: false,
  editable: false,
  ellipsis: false,
  text: ''
})

// Emits 定义
interface Emits {
  /** 点击事件 */
  click: [event: Event]
  /** 复制事件 */
  copy: [options: { text: string }]
  /** 开始编辑事件 */
  'edit-start': []
  /** 结束编辑事件 */
  'edit-end': [value: string]
  /** 取消编辑事件 */
  'edit-cancel': []
  /** 编辑内容变化事件 */
  'edit-change': [value: string]
  /** 展开/收起事件 */
  expand: [options: { expanded: boolean }]
}

const emit = defineEmits<Emits>()

// 组件注册
const ATooltip = Tooltip

// 获取 slots
const slots = defineSlots<{
  default?: () => any
}>()

// 响应式数据
const isEditing: Ref<boolean> = ref(false)
const editValue: Ref<string> = ref('')
const isEllipsis: Ref<boolean> = ref(false)
const isExpanded: Ref<boolean> = ref(false)
const isCopied: Ref<boolean> = ref(false)
const displayText: Ref<string> = ref('')
const originalText: Ref<string> = ref('')
const canExpand: Ref<boolean> = ref(false)
const hasEllipsis: Ref<boolean> = ref(false) // 跟踪文本是否需要省略（即使在展开状态下）

// 引用
const textContent: Ref<HTMLElement | null> = ref(null)
const editInput: Ref<HTMLInputElement | HTMLTextAreaElement | null> = ref(null)
    
// 计算属性
const tag: ComputedRef<string> = computed(() => {
  if (props.code) return 'code'
  if (props.keyboard) return 'kbd'
  if (props.mark) return 'mark'
  return 'span'
})

// 复制配置
const copyableConfig: ComputedRef<CopyableConfig | null> = computed(() => {
      if (!props.copyable) return null
      if (typeof props.copyable === 'boolean') {
        return {
          text: props.text || String(slots.default?.()[0]?.children || ''),
          onCopy: undefined,
          icon: undefined,
          tooltips: ['Copy', 'Copied'] as [string, string],
          format: 'text/plain' as const
        }
      }
      return {
        text: props.copyable.text || props.text || String(slots.default?.()[0]?.children || ''),
        onCopy: props.copyable.onCopy,
        icon: props.copyable.icon,
        tooltips: props.copyable.tooltips || (['Copy', 'Copied'] as [string, string]),
        format: props.copyable.format || ('text/plain' as const)
      }
    })
    
    // 编辑配置
    const editableConfig: ComputedRef<EditableConfig | null> = computed(() => {
      if (!props.editable) return null
      if (typeof props.editable === 'boolean') {
        return {
          icon: '✏️',
          tooltip: 'Edit',
          editing: false,
          maxLength: undefined,
          autoSize: true,
          text: props.text || String(slots.default?.()[0]?.children || ''),
          onChange: undefined,
          onCancel: undefined,
          onStart: undefined,
          onEnd: undefined,
          triggerType: ['icon'] as ('icon' | 'text')[],
          enterIcon: '✓'
        }
      }
      return {
        icon: props.editable.icon || '✏️',
        tooltip: props.editable.tooltip || 'Edit',
        editing: props.editable.editing || false,
        maxLength: props.editable.maxLength,
        autoSize: props.editable.autoSize !== false,
        text: props.editable.text || props.text || String(slots.default?.()[0]?.children || ''),
        onChange: props.editable.onChange,
        onCancel: props.editable.onCancel,
        onStart: props.editable.onStart,
        onEnd: props.editable.onEnd,
        triggerType: props.editable.triggerType || ['icon'],
        enterIcon: props.editable.enterIcon || '✓'
      }
    })
    
    // 省略配置
    const ellipsisConfig: ComputedRef<EllipsisConfig | null> = computed(() => {
      if (!props.ellipsis) return null
      if (typeof props.ellipsis === 'boolean') {
        return {
          rows: 1,
          expandable: false,
          suffix: '',
          symbol: 'Expand',
          tooltip: true,
          onExpand: undefined,
          onEllipsis: undefined
        }
      }
      return {
        rows: props.ellipsis.rows || 1,
        expandable: props.ellipsis.expandable || false,
        suffix: props.ellipsis.suffix || '',
        symbol: props.ellipsis.symbol || 'Expand',
        tooltip: props.ellipsis.tooltip !== undefined ? props.ellipsis.tooltip : true,
        onExpand: props.ellipsis.onExpand,
        onEllipsis: props.ellipsis.onEllipsis
      }
    })
    
    // 计算样式类
    const textClasses: ComputedRef<string[]> = computed(() => {
      const classes: string[] = ['ant-typography']

      if (props.code) classes.push('ant-typography-code')
      if (props.delete) classes.push('ant-typography-delete')
      if (props.disabled) classes.push('ant-typography-disabled')
      if (props.keyboard) classes.push('ant-typography-keyboard')
      if (props.mark) classes.push('ant-typography-mark')
      if (props.strong) classes.push('ant-typography-strong')
      if (props.italic) classes.push('ant-typography-italic')
      if (props.underline) classes.push('ant-typography-underline')
      if (props.type) classes.push(`ant-typography-${props.type}`)
      if (isEditing.value) classes.push('ant-typography-edit-content')

      // 省略功能需要特殊的显示方式
      if (ellipsisConfig.value) {
        classes.push('ant-typography-ellipsis-container')

        if (isExpanded.value) {
          // 展开状态
          classes.push('ant-typography-expanded')
        } else if (isEllipsis.value) {
          // 省略状态
          if (ellipsisConfig.value.rows === 1) {
            classes.push('ant-typography-ellipsis-single-line')
          } else {
            classes.push('ant-typography-ellipsis-multi-line')
          }
        }
      }

      return classes
    })

    // 计算样式
    const textStyles: ComputedRef<Record<string, string>> = computed(() => {
      const styles: Record<string, string> = {}

      // 只有在省略状态且未展开时才应用省略样式
      if (ellipsisConfig.value && isEllipsis.value && !isExpanded.value) {
        if (ellipsisConfig.value.rows === 1) {
          // 单行省略
          styles.overflow = 'hidden'
          styles.whiteSpace = 'nowrap'
          styles.textOverflow = 'ellipsis'
          styles.display = 'block'
          styles.width = '100%'
        } else {
          // 多行省略
          styles.display = '-webkit-box'
          styles.webkitLineClamp = String(ellipsisConfig.value.rows)
          styles.webkitBoxOrient = 'vertical'
          styles.overflow = 'hidden'
          styles.wordBreak = 'break-word'
        }
      }

      return styles
    })

    // 文本区域样式
    const textareaStyle: ComputedRef<Record<string, string>> = computed(() => {
      const styles: Record<string, string> = {
        border: 'none',
        outline: 'none',
        resize: 'none',
        background: 'transparent',
        fontFamily: 'inherit',
        fontSize: 'inherit',
        lineHeight: 'inherit',
        color: 'inherit'
      }

      if (editableConfig.value?.autoSize) {
        styles.minHeight = '1.5em'
        styles.height = 'auto'
      }

      return styles
    })

    // 复制提示文本
    const copyTooltip: ComputedRef<string> = computed(() => {
      if (!copyableConfig.value?.tooltips) return ''
      return isCopied.value ? copyableConfig.value.tooltips[1] : copyableConfig.value.tooltips[0]
    })

    // 复制图标
    const copyIcon: ComputedRef<string> = computed(() => {
      if (!copyableConfig.value?.icon) return '📋'
      return Array.isArray(copyableConfig.value.icon)
        ? (isCopied.value ? copyableConfig.value.icon[1] : copyableConfig.value.icon[0])
        : copyableConfig.value.icon
    })

    // 展开文本
    const expandText: ComputedRef<string> = computed(() => {
      if (typeof ellipsisConfig.value?.symbol === 'function') {
        return ellipsisConfig.value.symbol(isExpanded.value)
      }
      return isExpanded.value ? 'Collapse' : (ellipsisConfig.value?.symbol || 'Expand')
    })

    // Tooltip 相关计算属性
    const shouldShowTooltip: ComputedRef<boolean> = computed(() => {
      if (!ellipsisConfig.value) return false
      return ellipsisConfig.value.tooltip !== false
    })

    const tooltipTitle: ComputedRef<string> = computed(() => {
      if (!ellipsisConfig.value || !shouldShowTooltip.value) return ''

      // 如果 tooltip 是字符串，使用该字符串作为标题
      if (typeof ellipsisConfig.value.tooltip === 'string') {
        return ellipsisConfig.value.tooltip
      }

      // 否则使用原始文本作为标题
      return originalText.value
    })

    // 方法实现
    function handleClick(event: Event): void {
      if (props.disabled) return

      // 如果配置了文本触发编辑
      if (editableConfig.value && editableConfig.value.triggerType?.includes('text') && !isEditing.value) {
        handleEditStart()
        return
      }

      emit('click', event)
    }

    async function handleCopy(): Promise<void> {
      if (!copyableConfig.value) return

      try {
        const textToCopy = typeof copyableConfig.value.text === 'function'
          ? await copyableConfig.value.text()
          : copyableConfig.value.text || originalText.value

        if (copyableConfig.value.format === 'text/html') {
          await navigator.clipboard.write([
            new ClipboardItem({
              'text/html': new Blob([textToCopy], { type: 'text/html' }),
              'text/plain': new Blob([textToCopy.replace(/<[^>]*>/g, '')], { type: 'text/plain' })
            })
          ])
        } else {
          await navigator.clipboard.writeText(textToCopy)
        }

        isCopied.value = true
        setTimeout(() => {
          isCopied.value = false
        }, 2000)

        emit('copy', { text: textToCopy })
        copyableConfig.value.onCopy?.(null, { text: textToCopy })
      } catch (error) {
        console.error('Failed to copy text:', error)
      }
    }

    function handleEditStart(): void {
      if (props.disabled || isEditing.value) return

      editValue.value = editableConfig.value?.text || props.text || String(slots.default?.()[0]?.children || '')
      isEditing.value = true

      nextTick(() => {
        if (editInput.value) {
          editInput.value.focus()
          editInput.value.select()
        }
      })

      emit('edit-start')
      editableConfig.value?.onStart?.()
    }

    function handleEditBlur(): void {
      if (!isEditing.value) return
      handleEditConfirm()
    }

    function handleEditKeydown(event: KeyboardEvent): void {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault()
        handleEditConfirm()
      } else if (event.key === 'Escape') {
        event.preventDefault()
        handleEditCancel()
      }
    }

    function handleEditInput(event: Event): void {
      const target = event.target as HTMLInputElement | HTMLTextAreaElement
      editValue.value = target.value
      emit('edit-change', editValue.value)
      editableConfig.value?.onChange?.(editValue.value)
    }

    function handleEditConfirm(): void {
      if (!isEditing.value) return

      isEditing.value = false
      displayText.value = editValue.value

      emit('edit-end', editValue.value)
      editableConfig.value?.onEnd?.(editValue.value)
    }

    function handleEditCancel(): void {
      if (!isEditing.value) return

      isEditing.value = false
      editValue.value = editableConfig.value?.text || props.text || ''

      emit('edit-cancel')
      editableConfig.value?.onCancel?.()
    }

    function handleExpand(): void {
      isExpanded.value = !isExpanded.value

      if (isExpanded.value) {
        // 展开时显示完整文本，包括后缀
        displayText.value = originalText.value + (ellipsisConfig.value?.suffix || '')
        isEllipsis.value = false
      } else {
        // 收起时重新检查省略
        checkEllipsis()
      }

      emit('expand', { expanded: isExpanded.value })
      ellipsisConfig.value?.onExpand?.(null, { expanded: isExpanded.value })
    }

    // 检查是否需要省略
    function checkEllipsis(): void {
      if (!ellipsisConfig.value) {
        isEllipsis.value = false
        canExpand.value = false
        displayText.value = originalText.value
        return
      }

      if (isExpanded.value) {
        isEllipsis.value = false
        canExpand.value = ellipsisConfig.value?.expandable || false
        displayText.value = originalText.value + (ellipsisConfig.value?.suffix || '')
        return
      }

      nextTick(() => {
        if (!textContent.value) {
          return
        }

        const element = textContent.value

        // 创建测试元素来检测是否需要省略
        const testElement = document.createElement('div')
        testElement.style.cssText = `
          position: absolute;
          top: -9999px;
          left: -9999px;
          visibility: hidden;
          width: ${element.parentElement?.clientWidth || 400}px;
          font-family: ${getComputedStyle(element).fontFamily};
          font-size: ${getComputedStyle(element).fontSize};
          font-weight: ${getComputedStyle(element).fontWeight};
          line-height: ${getComputedStyle(element).lineHeight};
        `

        if (ellipsisConfig.value?.rows === 1) {
          // 单行省略检测 - 使用与多行相同的 CSS 方案
          testElement.style.cssText += `
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          `
          testElement.textContent = originalText.value
          document.body.appendChild(testElement)

          // 检测是否发生了省略
          const needsEllipsis = testElement.scrollWidth > testElement.clientWidth

          if (needsEllipsis) {
            isEllipsis.value = true
            hasEllipsis.value = true
            canExpand.value = ellipsisConfig.value?.expandable || false
            // 对于单行省略，让 CSS 处理截断，我们只设置原始文本
            displayText.value = originalText.value + (ellipsisConfig.value?.suffix || '')
          } else {
            isEllipsis.value = false
            hasEllipsis.value = false
            canExpand.value = false
            displayText.value = originalText.value + (ellipsisConfig.value?.suffix || '')
          }
        } else {
          // 多行省略检测
          testElement.style.cssText += `
            display: -webkit-box;
            -webkit-line-clamp: ${ellipsisConfig.value?.rows || 1};
            -webkit-box-orient: vertical;
            overflow: hidden;
            word-break: break-word;
          `
          testElement.textContent = originalText.value
          document.body.appendChild(testElement)

          const lineHeight = parseInt(getComputedStyle(testElement).lineHeight) || 20
          const maxHeight = lineHeight * (ellipsisConfig.value?.rows || 1)
          const needsEllipsis = testElement.scrollHeight > maxHeight

          if (needsEllipsis) {
            isEllipsis.value = true
            hasEllipsis.value = true
            canExpand.value = ellipsisConfig.value?.expandable || false
            displayText.value = originalText.value + (ellipsisConfig.value?.suffix || '')
          } else {
            isEllipsis.value = false
            hasEllipsis.value = false
            canExpand.value = false
            displayText.value = originalText.value + (ellipsisConfig.value?.suffix || '')
          }
        }

        document.body.removeChild(testElement)
        ellipsisConfig.value?.onEllipsis?.(isEllipsis.value)
      })
    }

    // 截断单行文本
    function truncateText(text: string, testElement: HTMLElement, containerWidth: number): string {
      if (!text) return ''

      const suffix = ellipsisConfig.value?.suffix || ''
      const ellipsisSymbol = '...'

      // 二分查找最佳截断位置
      let left = 0
      let right = text.length
      let result = ''

      while (left <= right) {
        const mid = Math.floor((left + right) / 2)
        const testText = text.slice(0, mid) + ellipsisSymbol + suffix
        testElement.textContent = testText

        if (testElement.scrollWidth <= containerWidth) {
          result = text.slice(0, mid)
          left = mid + 1
        } else {
          right = mid - 1
        }
      }

      return result + ellipsisSymbol + suffix
    }

    // 截断多行文本
    function truncateMultilineText(text: string, testElement: HTMLElement, rows: number): string {
      if (!text) return ''

      const suffix = ellipsisConfig.value?.suffix || ''
      const ellipsisSymbol = '...'

      // 对于多行省略，我们使用 CSS line-clamp，不需要手动截断
      // 这里返回原文本，让 CSS 处理省略
      return text + ellipsisSymbol + suffix
    }

    // 生命周期
    onMounted(() => {
      originalText.value = props.text || String(slots.default?.()[0]?.children || '')
      displayText.value = originalText.value

      if (ellipsisConfig.value) {
        // 使用 nextTick 和小延迟确保 DOM 已经完全渲染
        nextTick(() => {
          setTimeout(() => {
            checkEllipsis()

            // 监听窗口大小变化，重新计算省略
            const resizeObserver = new ResizeObserver(() => {
              checkEllipsis()
            })

            if (textContent.value?.parentElement) {
              resizeObserver.observe(textContent.value.parentElement)
            }

            // 清理函数
            onUnmounted(() => {
              resizeObserver.disconnect()
            })
          }, 10) // 10ms 延迟确保 DOM 稳定
        })
      }

      if (editableConfig.value?.editing) {
        handleEditStart()
      }
    })

    // 监听文本变化
    watch(() => props.text, (newText) => {
      originalText.value = newText || ''
      displayText.value = originalText.value
      if (ellipsisConfig.value) {
        nextTick(() => {
          checkEllipsis()
        })
      }
    })

    // 监听省略配置变化
    watch(() => props.ellipsis, () => {
      if (ellipsisConfig.value) {
        nextTick(() => {
          checkEllipsis()
        })
      }
    }, { deep: true })


</script>

<style scoped>
/* 基础 Typography 样式 */
.ant-typography {
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  line-height: 1.5715;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
  margin: 0;
  padding: 0;
  display: inline;
  word-break: break-word;
}

/* 省略容器样式 */
.ant-typography-ellipsis-container {
  display: block;
  width: 100%;
  max-width: 100%;
}

/* 省略内容样式 */
.ant-typography-ellipsis-content {
  display: inline-block;
  width: 100%;
  max-width: 100%;
}

/* 代码样式 */
.ant-typography-code {
  margin: 0 0.2em;
  padding: 0.2em 0.4em 0.1em;
  font-size: 85%;
  background: rgba(150, 150, 150, 0.1);
  border: 1px solid rgba(100, 100, 100, 0.2);
  border-radius: 3px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
}

/* 标记样式 */
.ant-typography-mark {
  background-color: #ffe58f;
  padding: 0;
}

/* 删除线样式 */
.ant-typography-delete {
  text-decoration: line-through;
  text-decoration-color: rgba(0, 0, 0, 0.85);
}

/* 下划线样式 */
.ant-typography-underline {
  text-decoration: underline;
  text-decoration-color: rgba(0, 0, 0, 0.85);
}

/* 粗体样式 */
.ant-typography-strong {
  font-weight: 600;
}

/* 斜体样式 */
.ant-typography-italic {
  font-style: italic;
}

/* 键盘样式 */
.ant-typography-keyboard {
  margin: 0 0.2em;
  padding: 0.15em 0.4em 0.1em;
  font-size: 90%;
  background: rgba(150, 150, 150, 0.06);
  border: 1px solid rgba(100, 100, 100, 0.2);
  border-bottom-color: rgba(100, 100, 100, 0.25);
  border-radius: 3px;
  box-shadow: inset 0 -1px 0 rgba(100, 100, 100, 0.25);
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
}

/* 禁用状态 */
.ant-typography-disabled {
  color: rgba(0, 0, 0, 0.25);
  cursor: not-allowed;
  user-select: none;
}

/* 类型样式 */
.ant-typography-secondary {
  color: rgba(0, 0, 0, 0.45);
}

.ant-typography-success {
  color: #52c41a;
}

.ant-typography-warning {
  color: #faad14;
}

.ant-typography-danger {
  color: #ff4d4f;
}

/* 省略样式 */
.ant-typography-ellipsis {
  display: block;
  width: 100%;
  max-width: 100%;
}

.ant-typography-ellipsis-single-line {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
  width: 100%;
  max-width: 100%;
}

.ant-typography-ellipsis-multi-line {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
  width: 100%;
  max-width: 100%;
}

.ant-typography-ellipsis-suffix {
  display: inline;
}

.ant-typography-expand {
  background: none;
  border: none;
  color: #1890ff;
  cursor: pointer;
  padding: 0;
  margin-left: 8px;
  font-size: inherit;
  line-height: inherit;
  text-decoration: none;
}

.ant-typography-expand:hover {
  color: #40a9ff;
}

.ant-typography-expand:active {
  color: #096dd9;
}

/* 展开状态的文本样式 */
.ant-typography-expanded {
  white-space: normal;
  word-wrap: break-word;
  word-break: break-word;
}

/* 复制按钮样式 */
.ant-typography-copy {
  background: none;
  border: none;
  color: rgba(0, 0, 0, 0.45);
  cursor: pointer;
  padding: 0;
  margin-left: 8px;
  font-size: 12px;
  line-height: 1;
  transition: color 0.3s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
}

.ant-typography-copy:hover {
  color: rgba(0, 0, 0, 0.85);
}

.ant-typography-copy:active {
  color: #1890ff;
}

/* 编辑按钮样式 */
.ant-typography-edit {
  background: none;
  border: none;
  color: rgba(0, 0, 0, 0.45);
  cursor: pointer;
  padding: 0;
  margin-left: 8px;
  font-size: 12px;
  line-height: 1;
  transition: color 0.3s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
}

.ant-typography-edit:hover {
  color: rgba(0, 0, 0, 0.85);
}

.ant-typography-edit:active {
  color: #1890ff;
}

/* 编辑状态样式 */
.ant-typography-edit-content {
  position: relative;
}

.ant-typography-edit-content textarea {
  width: 100%;
  min-height: 1.5em;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 4px 8px;
  font-size: inherit;
  line-height: inherit;
  font-family: inherit;
  color: inherit;
  background: #fff;
  transition: border-color 0.3s;
  resize: none;
}

.ant-typography-edit-content textarea:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  outline: none;
}

.ant-typography-edit-enter {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: #1890ff;
  cursor: pointer;
  font-size: 12px;
  line-height: 1;
  padding: 2px;
}

.ant-typography-edit-enter:hover {
  color: #40a9ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-typography {
    font-size: 13px;
  }

  .ant-typography-copy,
  .ant-typography-edit {
    width: 18px;
    height: 18px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .ant-typography {
    font-size: 12px;
  }

  .ant-typography-code,
  .ant-typography-keyboard {
    font-size: 80%;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .ant-typography {
    color: rgba(255, 255, 255, 0.85);
  }

  .ant-typography-secondary {
    color: rgba(255, 255, 255, 0.45);
  }

  .ant-typography-disabled {
    color: rgba(255, 255, 255, 0.25);
  }

  .ant-typography-code {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
  }

  .ant-typography-keyboard {
    background: rgba(255, 255, 255, 0.06);
    border-color: rgba(255, 255, 255, 0.2);
    border-bottom-color: rgba(255, 255, 255, 0.25);
    box-shadow: inset 0 -1px 0 rgba(255, 255, 255, 0.25);
  }

  .ant-typography-mark {
    background-color: #d4b106;
    color: #000;
  }

  .ant-typography-delete {
    text-decoration-color: rgba(255, 255, 255, 0.85);
  }

  .ant-typography-underline {
    text-decoration-color: rgba(255, 255, 255, 0.85);
  }

  .ant-typography-copy,
  .ant-typography-edit {
    color: rgba(255, 255, 255, 0.45);
  }

  .ant-typography-copy:hover,
  .ant-typography-edit:hover {
    color: rgba(255, 255, 255, 0.85);
  }

  .ant-typography-edit-content textarea {
    background: #141414;
    border-color: #434343;
    color: rgba(255, 255, 255, 0.85);
  }

  .ant-typography-edit-content textarea:focus {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .ant-typography {
    color: #000;
  }

  .ant-typography-secondary {
    color: #666;
  }

  .ant-typography-disabled {
    color: #999;
  }

  .ant-typography-code,
  .ant-typography-keyboard {
    border-color: #000;
    background: #f0f0f0;
  }

  .ant-typography-copy,
  .ant-typography-edit,
  .ant-typography-expand {
    color: #0066cc;
  }

  .ant-typography-copy:hover,
  .ant-typography-edit:hover,
  .ant-typography-expand:hover {
    color: #004499;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .ant-typography-copy,
  .ant-typography-edit,
  .ant-typography-expand,
  .ant-typography-edit-content textarea {
    transition: none;
  }
}
</style>
