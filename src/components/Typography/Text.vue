<template>
  <component
    :is="tag"
    :class="textClasses"
    :style="textStyles"
    @click="handleClick"
    v-bind="$attrs"
  >
    <!-- 可编辑状态 -->
    <template v-if="isEditing">
      <textarea
        ref="editInput"
        v-model="editValue"
        :maxlength="editableConfig.maxLength"
        :style="textareaStyle"
        @blur="handleEditBlur"
        @keydown="handleEditKeydown"
        @input="handleEditInput"
      />
      <span v-if="editableConfig.enterIcon" class="ant-typography-edit-enter" @click="handleEditConfirm">
        {{ editableConfig.enterIcon }}
      </span>
    </template>
    
    <!-- 正常显示状态 -->
    <template v-else>
      <!-- 省略号处理的文本内容 -->
      <template v-if="ellipsisConfig">
        <template v-if="isEllipsis && !isExpanded">
          <a-tooltip v-if="ellipsisConfig.tooltip !== false" :title="originalText">
            <span ref="textContent" class="ant-typography-ellipsis-content">{{ displayText }}</span>
          </a-tooltip>
          <span v-else ref="textContent" class="ant-typography-ellipsis-content">{{ displayText }}</span>
          <button
            v-if="ellipsisConfig.expandable"
            class="ant-typography-expand"
            @click="handleExpand"
          >
            {{ expandText }}
          </button>
        </template>
        <template v-else>
          <span ref="textContent">{{ originalText }}{{ ellipsisConfig.suffix || '' }}</span>
          <button
            v-if="ellipsisConfig.expandable && isEllipsis"
            class="ant-typography-expand"
            @click="handleExpand"
          >
            {{ expandText }}
          </button>
        </template>
      </template>

      <!-- 普通文本内容 -->
      <span v-else ref="textContent">
        <slot>{{ text || displayText }}</slot>
      </span>
      
      <!-- 复制按钮 -->
      <button
        v-if="copyableConfig"
        class="ant-typography-copy"
        :title="copyTooltip"
        @click="handleCopy"
      >
        <span v-if="copyableConfig.icon">{{ copyIcon }}</span>
        <span v-else>📋</span>
      </button>
      
      <!-- 编辑按钮 -->
      <button
        v-if="editableConfig && !isEditing && editableConfig.triggerType.includes('icon')"
        class="ant-typography-edit"
        :title="editableConfig.tooltip"
        @click="handleEditStart"
      >
        <span v-if="editableConfig.icon">{{ editableConfig.icon }}</span>
        <span v-else>✏️</span>
      </button>
    </template>
  </component>
</template>

<script>
import { ref, computed, nextTick, onMounted, onUnmounted, watch } from 'vue'
import { Tooltip } from 'ant-design-vue'

export default {
  name: 'TypographyText',
  components: {
    'a-tooltip': Tooltip
  },
  inheritAttrs: false,
  props: {
    // 基础样式属性
    code: {
      type: Boolean,
      default: false
    },
    copyable: {
      type: [Boolean, Object],
      default: false
    },
    delete: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    editable: {
      type: [Boolean, Object],
      default: false
    },
    ellipsis: {
      type: [Boolean, Object],
      default: false
    },
    keyboard: {
      type: Boolean,
      default: false
    },
    mark: {
      type: Boolean,
      default: false
    },
    strong: {
      type: Boolean,
      default: false
    },
    italic: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      validator: value => ['secondary', 'success', 'warning', 'danger'].includes(value)
    },
    underline: {
      type: Boolean,
      default: false
    },
    // 内容
    text: {
      type: String,
      default: ''
    }
  },
  emits: ['click', 'copy', 'edit-start', 'edit-end', 'edit-cancel', 'edit-change', 'expand'],
  setup(props, { emit, slots }) {
    // 响应式数据
    const isEditing = ref(false)
    const editValue = ref('')
    const isEllipsis = ref(false)
    const isExpanded = ref(false)
    const isCopied = ref(false)
    const displayText = ref('')
    const originalText = ref('')
    const canExpand = ref(false)
    
    // 引用
    const textContent = ref(null)
    const editInput = ref(null)
    
    // 计算属性
    const tag = computed(() => {
      if (props.code) return 'code'
      if (props.keyboard) return 'kbd'
      if (props.mark) return 'mark'
      return 'span'
    })
    
    // 复制配置
    const copyableConfig = computed(() => {
      if (!props.copyable) return null
      if (typeof props.copyable === 'boolean') {
        return {
          text: props.text || (slots.default?.()[0]?.children || ''),
          onCopy: null,
          icon: null,
          tooltips: ['Copy', 'Copied'],
          format: 'text/plain'
        }
      }
      return {
        text: props.copyable.text || props.text || (slots.default?.()[0]?.children || ''),
        onCopy: props.copyable.onCopy,
        icon: props.copyable.icon,
        tooltips: props.copyable.tooltips || ['Copy', 'Copied'],
        format: props.copyable.format || 'text/plain'
      }
    })
    
    // 编辑配置
    const editableConfig = computed(() => {
      if (!props.editable) return null
      if (typeof props.editable === 'boolean') {
        return {
          icon: '✏️',
          tooltip: 'Edit',
          editing: false,
          maxLength: undefined,
          autoSize: true,
          text: props.text || (slots.default?.()[0]?.children || ''),
          onChange: null,
          onCancel: null,
          onStart: null,
          onEnd: null,
          triggerType: ['icon'],
          enterIcon: '✓'
        }
      }
      return {
        icon: props.editable.icon || '✏️',
        tooltip: props.editable.tooltip || 'Edit',
        editing: props.editable.editing || false,
        maxLength: props.editable.maxLength,
        autoSize: props.editable.autoSize !== false,
        text: props.editable.text || props.text || (slots.default?.()[0]?.children || ''),
        onChange: props.editable.onChange,
        onCancel: props.editable.onCancel,
        onStart: props.editable.onStart,
        onEnd: props.editable.onEnd,
        triggerType: props.editable.triggerType || ['icon'],
        enterIcon: props.editable.enterIcon || '✓'
      }
    })
    
    // 省略配置
    const ellipsisConfig = computed(() => {
      if (!props.ellipsis) return null
      if (typeof props.ellipsis === 'boolean') {
        return {
          rows: 1,
          expandable: false,
          suffix: '',
          symbol: 'Expand',
          tooltip: true,
          onExpand: null,
          onEllipsis: null
        }
      }
      return {
        rows: props.ellipsis.rows || 1,
        expandable: props.ellipsis.expandable || false,
        suffix: props.ellipsis.suffix || '',
        symbol: props.ellipsis.symbol || 'Expand',
        tooltip: props.ellipsis.tooltip !== false,
        onExpand: props.ellipsis.onExpand,
        onEllipsis: props.ellipsis.onEllipsis
      }
    })
    
    // 计算样式类
    const textClasses = computed(() => {
      const classes = ['ant-typography']

      if (props.code) classes.push('ant-typography-code')
      if (props.delete) classes.push('ant-typography-delete')
      if (props.disabled) classes.push('ant-typography-disabled')
      if (props.keyboard) classes.push('ant-typography-keyboard')
      if (props.mark) classes.push('ant-typography-mark')
      if (props.strong) classes.push('ant-typography-strong')
      if (props.italic) classes.push('ant-typography-italic')
      if (props.underline) classes.push('ant-typography-underline')
      if (props.type) classes.push(`ant-typography-${props.type}`)
      if (isEllipsis.value) classes.push('ant-typography-ellipsis-single-line')
      if (isEditing.value) classes.push('ant-typography-edit-content')

      return classes
    })

    // 计算样式
    const textStyles = computed(() => {
      const styles = {}
      if (ellipsisConfig.value) {
        if (ellipsisConfig.value.rows === 1) {
          // 单行省略
          styles.overflow = 'hidden'
          styles.whiteSpace = 'nowrap'
          styles.textOverflow = 'ellipsis'
        } else {
          // 多行省略
          styles.display = '-webkit-box'
          styles.webkitLineClamp = ellipsisConfig.value.rows
          styles.webkitBoxOrient = 'vertical'
          styles.overflow = 'hidden'
          styles.wordBreak = 'break-word'
        }
      }
      return styles
    })

    // 文本区域样式
    const textareaStyle = computed(() => {
      const styles = {
        border: 'none',
        outline: 'none',
        resize: 'none',
        background: 'transparent',
        fontFamily: 'inherit',
        fontSize: 'inherit',
        lineHeight: 'inherit',
        color: 'inherit'
      }

      if (editableConfig.value?.autoSize) {
        styles.minHeight = '1.5em'
        styles.height = 'auto'
      }

      return styles
    })

    // 复制提示文本
    const copyTooltip = computed(() => {
      if (!copyableConfig.value?.tooltips) return ''
      return isCopied.value ? copyableConfig.value.tooltips[1] : copyableConfig.value.tooltips[0]
    })

    // 复制图标
    const copyIcon = computed(() => {
      if (!copyableConfig.value?.icon) return '📋'
      return Array.isArray(copyableConfig.value.icon)
        ? (isCopied.value ? copyableConfig.value.icon[1] : copyableConfig.value.icon[0])
        : copyableConfig.value.icon
    })

    // 展开文本
    const expandText = computed(() => {
      if (typeof ellipsisConfig.value?.symbol === 'function') {
        return ellipsisConfig.value.symbol(isExpanded.value)
      }
      return isExpanded.value ? 'Collapse' : (ellipsisConfig.value?.symbol || 'Expand')
    })

    // 方法实现
    function handleClick(event) {
      if (props.disabled) return

      // 如果配置了文本触发编辑
      if (editableConfig.value && editableConfig.value.triggerType.includes('text') && !isEditing.value) {
        handleEditStart()
        return
      }

      emit('click', event)
    }

    async function handleCopy() {
      if (!copyableConfig.value) return

      try {
        const textToCopy = typeof copyableConfig.value.text === 'function'
          ? await copyableConfig.value.text()
          : copyableConfig.value.text

        if (copyableConfig.value.format === 'text/html') {
          await navigator.clipboard.write([
            new ClipboardItem({
              'text/html': new Blob([textToCopy], { type: 'text/html' }),
              'text/plain': new Blob([textToCopy.replace(/<[^>]*>/g, '')], { type: 'text/plain' })
            })
          ])
        } else {
          await navigator.clipboard.writeText(textToCopy)
        }

        isCopied.value = true
        setTimeout(() => {
          isCopied.value = false
        }, 2000)

        emit('copy', { text: textToCopy })
        copyableConfig.value.onCopy?.(null, { text: textToCopy })
      } catch (error) {
        console.error('Failed to copy text:', error)
      }
    }

    function handleEditStart() {
      if (props.disabled || isEditing.value) return

      editValue.value = editableConfig.value?.text || props.text || (slots.default?.()[0]?.children || '')
      isEditing.value = true

      nextTick(() => {
        if (editInput.value) {
          editInput.value.focus()
          editInput.value.select()
        }
      })

      emit('edit-start')
      editableConfig.value?.onStart?.()
    }

    function handleEditBlur() {
      if (!isEditing.value) return
      handleEditConfirm()
    }

    function handleEditKeydown(event) {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault()
        handleEditConfirm()
      } else if (event.key === 'Escape') {
        event.preventDefault()
        handleEditCancel()
      }
    }

    function handleEditInput(event) {
      editValue.value = event.target.value
      emit('edit-change', editValue.value)
      editableConfig.value?.onChange?.(editValue.value)
    }

    function handleEditConfirm() {
      if (!isEditing.value) return

      isEditing.value = false
      displayText.value = editValue.value

      emit('edit-end', editValue.value)
      editableConfig.value?.onEnd?.(editValue.value)
    }

    function handleEditCancel() {
      if (!isEditing.value) return

      isEditing.value = false
      editValue.value = editableConfig.value?.text || props.text || ''

      emit('edit-cancel')
      editableConfig.value?.onCancel?.()
    }

    function handleExpand() {
      isExpanded.value = !isExpanded.value

      if (isExpanded.value) {
        displayText.value = originalText.value
        isEllipsis.value = false
      } else {
        checkEllipsis()
      }

      emit('expand', { expanded: isExpanded.value })
      ellipsisConfig.value?.onExpand?.(null, { expanded: isExpanded.value })
    }

    // 检查是否需要省略
    function checkEllipsis() {
      if (!ellipsisConfig.value) {
        isEllipsis.value = false
        canExpand.value = false
        displayText.value = originalText.value
        return
      }

      if (isExpanded.value) {
        isEllipsis.value = false
        canExpand.value = ellipsisConfig.value.expandable
        displayText.value = originalText.value + (ellipsisConfig.value.suffix || '')
        return
      }

      nextTick(() => {
        if (!textContent.value) return

        const element = textContent.value

        // 创建测试元素来检测是否需要省略
        const testElement = document.createElement('div')
        testElement.style.cssText = `
          position: absolute;
          top: -9999px;
          left: -9999px;
          visibility: hidden;
          width: ${element.parentElement?.clientWidth || 200}px;
          font-family: ${getComputedStyle(element).fontFamily};
          font-size: ${getComputedStyle(element).fontSize};
          font-weight: ${getComputedStyle(element).fontWeight};
          line-height: ${getComputedStyle(element).lineHeight};
        `
        testElement.textContent = originalText.value
        document.body.appendChild(testElement)

        if (ellipsisConfig.value.rows === 1) {
          // 单行省略检测
          testElement.style.whiteSpace = 'nowrap'
          const needsEllipsis = testElement.scrollWidth > (element.parentElement?.clientWidth || 200)

          if (needsEllipsis) {
            isEllipsis.value = true
            canExpand.value = ellipsisConfig.value.expandable
            displayText.value = truncateText(originalText.value, testElement, element.parentElement?.clientWidth || 200)
          } else {
            isEllipsis.value = false
            canExpand.value = false
            displayText.value = originalText.value + (ellipsisConfig.value.suffix || '')
          }
        } else {
          // 多行省略检测
          testElement.style.cssText += `
            display: -webkit-box;
            -webkit-line-clamp: ${ellipsisConfig.value.rows};
            -webkit-box-orient: vertical;
            overflow: hidden;
            word-break: break-word;
          `

          const lineHeight = parseInt(getComputedStyle(testElement).lineHeight) || 20
          const maxHeight = lineHeight * ellipsisConfig.value.rows
          const needsEllipsis = testElement.scrollHeight > maxHeight

          if (needsEllipsis) {
            isEllipsis.value = true
            canExpand.value = ellipsisConfig.value.expandable
            displayText.value = truncateMultilineText(originalText.value, testElement, ellipsisConfig.value.rows)
          } else {
            isEllipsis.value = false
            canExpand.value = false
            displayText.value = originalText.value + (ellipsisConfig.value.suffix || '')
          }
        }

        document.body.removeChild(testElement)
        ellipsisConfig.value?.onEllipsis?.(isEllipsis.value)
      })
    }

    // 截断单行文本
    function truncateText(text, testElement, containerWidth) {
      if (!text) return ''

      const suffix = ellipsisConfig.value?.suffix || ''
      const ellipsisSymbol = '...'

      // 二分查找最佳截断位置
      let left = 0
      let right = text.length
      let result = ''

      while (left <= right) {
        const mid = Math.floor((left + right) / 2)
        const testText = text.slice(0, mid) + ellipsisSymbol + suffix
        testElement.textContent = testText

        if (testElement.scrollWidth <= containerWidth) {
          result = text.slice(0, mid)
          left = mid + 1
        } else {
          right = mid - 1
        }
      }

      return result + ellipsisSymbol + suffix
    }

    // 截断多行文本
    function truncateMultilineText(text, testElement, rows) {
      if (!text) return ''

      const suffix = ellipsisConfig.value?.suffix || ''
      const ellipsisSymbol = '...'

      // 对于多行省略，我们使用 CSS line-clamp，不需要手动截断
      // 这里返回原文本，让 CSS 处理省略
      return text + ellipsisSymbol + suffix
    }

    // 生命周期
    onMounted(() => {
      originalText.value = props.text || (slots.default?.()[0]?.children || '')
      displayText.value = originalText.value

      if (ellipsisConfig.value) {
        checkEllipsis()
      }

      if (editableConfig.value?.editing) {
        handleEditStart()
      }
    })

    // 监听文本变化
    watch(() => props.text, (newText) => {
      originalText.value = newText || ''
      displayText.value = originalText.value
      if (ellipsisConfig.value) {
        checkEllipsis()
      }
    })

    // 监听省略配置变化
    watch(() => props.ellipsis, () => {
      if (ellipsisConfig.value) {
        checkEllipsis()
      }
    }, { deep: true })

    // 方法实现
    function handleClick(event) {
      if (props.disabled) return

      // 如果配置了文本触发编辑
      if (editableConfig.value && editableConfig.value.triggerType.includes('text') && !isEditing.value) {
        handleEditStart()
        return
      }

      emit('click', event)
    }

    async function handleCopy() {
      if (!copyableConfig.value) return

      try {
        const textToCopy = typeof copyableConfig.value.text === 'function'
          ? await copyableConfig.value.text()
          : copyableConfig.value.text

        if (copyableConfig.value.format === 'text/html') {
          await navigator.clipboard.write([
            new ClipboardItem({
              'text/html': new Blob([textToCopy], { type: 'text/html' }),
              'text/plain': new Blob([textToCopy.replace(/<[^>]*>/g, '')], { type: 'text/plain' })
            })
          ])
        } else {
          await navigator.clipboard.writeText(textToCopy)
        }

        isCopied.value = true
        setTimeout(() => {
          isCopied.value = false
        }, 2000)

        emit('copy', { text: textToCopy })
        copyableConfig.value.onCopy?.(null, { text: textToCopy })
      } catch (error) {
        console.error('Failed to copy text:', error)
      }
    }

    function handleEditStart() {
      if (props.disabled || isEditing.value) return

      editValue.value = editableConfig.value?.text || props.text || (slots.default?.()[0]?.children || '')
      isEditing.value = true

      nextTick(() => {
        if (editInput.value) {
          editInput.value.focus()
          editInput.value.select()
        }
      })

      emit('edit-start')
      editableConfig.value?.onStart?.()
    }

    function handleEditBlur() {
      if (!isEditing.value) return
      handleEditConfirm()
    }

    function handleEditKeydown(event) {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault()
        handleEditConfirm()
      } else if (event.key === 'Escape') {
        event.preventDefault()
        handleEditCancel()
      }
    }

    function handleEditInput(event) {
      editValue.value = event.target.value
      emit('edit-change', editValue.value)
      editableConfig.value?.onChange?.(editValue.value)
    }

    function handleEditConfirm() {
      if (!isEditing.value) return

      isEditing.value = false
      displayText.value = editValue.value

      emit('edit-end', editValue.value)
      editableConfig.value?.onEnd?.(editValue.value)
    }

    function handleEditCancel() {
      if (!isEditing.value) return

      isEditing.value = false
      editValue.value = editableConfig.value?.text || props.text || ''

      emit('edit-cancel')
      editableConfig.value?.onCancel?.()
    }

    function handleExpand() {
      isExpanded.value = !isExpanded.value

      if (isExpanded.value) {
        displayText.value = props.text || (slots.default?.()[0]?.children || '')
        isEllipsis.value = false
      } else {
        checkEllipsis()
      }

      emit('expand', { expanded: isExpanded.value })
      ellipsisConfig.value?.onExpand?.(null, { expanded: isExpanded.value })
    }

    // 检查是否需要省略
    function checkEllipsis() {
      if (!ellipsisConfig.value || isExpanded.value) {
        isEllipsis.value = false
        canExpand.value = false
        displayText.value = originalText.value
        return
      }

      nextTick(() => {
        if (!textContent.value) return

        const element = textContent.value
        const container = element.parentElement
        if (!container) return

        // 设置原始文本
        element.textContent = originalText.value

        if (ellipsisConfig.value.rows === 1) {
          // 单行省略检测
          const containerWidth = container.clientWidth
          const textWidth = element.scrollWidth

          if (textWidth > containerWidth) {
            isEllipsis.value = true
            canExpand.value = ellipsisConfig.value.expandable
            displayText.value = truncateText(originalText.value, element, containerWidth)
          } else {
            isEllipsis.value = false
            canExpand.value = false
            displayText.value = originalText.value
          }
        } else {
          // 多行省略检测
          const lineHeight = parseInt(getComputedStyle(element).lineHeight) || 20
          const maxHeight = lineHeight * ellipsisConfig.value.rows

          // 临时设置样式来检测高度
          const originalStyle = element.style.cssText
          element.style.cssText = `
            display: -webkit-box;
            -webkit-line-clamp: ${ellipsisConfig.value.rows};
            -webkit-box-orient: vertical;
            overflow: hidden;
            word-break: break-word;
          `

          const actualHeight = element.scrollHeight

          if (actualHeight > maxHeight) {
            isEllipsis.value = true
            canExpand.value = ellipsisConfig.value.expandable
            displayText.value = truncateMultilineText(originalText.value, element, ellipsisConfig.value.rows)
          } else {
            isEllipsis.value = false
            canExpand.value = false
            displayText.value = originalText.value
          }

          // 恢复原始样式
          element.style.cssText = originalStyle
        }

        ellipsisConfig.value?.onEllipsis?.(isEllipsis.value)
      })
    }

    // 截断单行文本
    function truncateText(text, element, containerWidth) {
      if (!text) return ''

      const suffix = ellipsisConfig.value?.suffix || ''
      const ellipsisSymbol = '...'

      // 创建临时测量元素
      const measureEl = document.createElement('span')
      measureEl.style.cssText = `
        position: absolute;
        top: -9999px;
        left: -9999px;
        visibility: hidden;
        white-space: nowrap;
        font-family: ${getComputedStyle(element).fontFamily};
        font-size: ${getComputedStyle(element).fontSize};
        font-weight: ${getComputedStyle(element).fontWeight};
      `
      document.body.appendChild(measureEl)

      // 二分查找最佳截断位置
      let left = 0
      let right = text.length
      let result = text

      while (left <= right) {
        const mid = Math.floor((left + right) / 2)
        const testText = text.slice(0, mid) + ellipsisSymbol + suffix
        measureEl.textContent = testText

        if (measureEl.scrollWidth <= containerWidth) {
          result = text.slice(0, mid)
          left = mid + 1
        } else {
          right = mid - 1
        }
      }

      document.body.removeChild(measureEl)
      return result + ellipsisSymbol + suffix
    }

    return {
      // 响应式数据
      isEditing,
      editValue,
      isEllipsis,
      isExpanded,
      isCopied,
      displayText,
      canExpand,
      originalText,

      // 引用
      textContent,
      editInput,

      // 计算属性
      tag,
      copyableConfig,
      editableConfig,
      ellipsisConfig,
      textClasses,
      textStyles,
      textareaStyle,
      copyTooltip,
      copyIcon,
      expandText,

      // 方法
      handleClick,
      handleCopy,
      handleEditStart,
      handleEditBlur,
      handleEditKeydown,
      handleEditInput,
      handleEditConfirm,
      handleExpand,
      checkEllipsis
    }
  }
}
</script>

<style scoped>
/* 基础 Typography 样式 */
.ant-typography {
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  line-height: 1.5715;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
  margin: 0;
  padding: 0;
  display: inline;
  word-break: break-word;
}

/* 代码样式 */
.ant-typography-code {
  margin: 0 0.2em;
  padding: 0.2em 0.4em 0.1em;
  font-size: 85%;
  background: rgba(150, 150, 150, 0.1);
  border: 1px solid rgba(100, 100, 100, 0.2);
  border-radius: 3px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
}

/* 标记样式 */
.ant-typography-mark {
  background-color: #ffe58f;
  padding: 0;
}

/* 删除线样式 */
.ant-typography-delete {
  text-decoration: line-through;
  text-decoration-color: rgba(0, 0, 0, 0.85);
}

/* 下划线样式 */
.ant-typography-underline {
  text-decoration: underline;
  text-decoration-color: rgba(0, 0, 0, 0.85);
}

/* 粗体样式 */
.ant-typography-strong {
  font-weight: 600;
}

/* 斜体样式 */
.ant-typography-italic {
  font-style: italic;
}

/* 键盘样式 */
.ant-typography-keyboard {
  margin: 0 0.2em;
  padding: 0.15em 0.4em 0.1em;
  font-size: 90%;
  background: rgba(150, 150, 150, 0.06);
  border: 1px solid rgba(100, 100, 100, 0.2);
  border-bottom-color: rgba(100, 100, 100, 0.25);
  border-radius: 3px;
  box-shadow: inset 0 -1px 0 rgba(100, 100, 100, 0.25);
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
}

/* 禁用状态 */
.ant-typography-disabled {
  color: rgba(0, 0, 0, 0.25);
  cursor: not-allowed;
  user-select: none;
}

/* 类型样式 */
.ant-typography-secondary {
  color: rgba(0, 0, 0, 0.45);
}

.ant-typography-success {
  color: #52c41a;
}

.ant-typography-warning {
  color: #faad14;
}

.ant-typography-danger {
  color: #ff4d4f;
}

/* 省略样式 */
.ant-typography-ellipsis {
  display: inline;
}

.ant-typography-ellipsis-content {
  display: inline;
}

.ant-typography-ellipsis-single-line {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.ant-typography-ellipsis-suffix {
  display: inline;
}

.ant-typography-expand {
  background: none;
  border: none;
  color: #1890ff;
  cursor: pointer;
  padding: 0;
  margin-left: 8px;
  font-size: inherit;
  line-height: inherit;
  text-decoration: none;
}

.ant-typography-expand:hover {
  color: #40a9ff;
}

.ant-typography-expand:active {
  color: #096dd9;
}

/* 复制按钮样式 */
.ant-typography-copy {
  background: none;
  border: none;
  color: rgba(0, 0, 0, 0.45);
  cursor: pointer;
  padding: 0;
  margin-left: 8px;
  font-size: 12px;
  line-height: 1;
  transition: color 0.3s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
}

.ant-typography-copy:hover {
  color: rgba(0, 0, 0, 0.85);
}

.ant-typography-copy:active {
  color: #1890ff;
}

/* 编辑按钮样式 */
.ant-typography-edit {
  background: none;
  border: none;
  color: rgba(0, 0, 0, 0.45);
  cursor: pointer;
  padding: 0;
  margin-left: 8px;
  font-size: 12px;
  line-height: 1;
  transition: color 0.3s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
}

.ant-typography-edit:hover {
  color: rgba(0, 0, 0, 0.85);
}

.ant-typography-edit:active {
  color: #1890ff;
}

/* 编辑状态样式 */
.ant-typography-edit-content {
  position: relative;
}

.ant-typography-edit-content textarea {
  width: 100%;
  min-height: 1.5em;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 4px 8px;
  font-size: inherit;
  line-height: inherit;
  font-family: inherit;
  color: inherit;
  background: #fff;
  transition: border-color 0.3s;
  resize: none;
}

.ant-typography-edit-content textarea:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  outline: none;
}

.ant-typography-edit-enter {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: #1890ff;
  cursor: pointer;
  font-size: 12px;
  line-height: 1;
  padding: 2px;
}

.ant-typography-edit-enter:hover {
  color: #40a9ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-typography {
    font-size: 13px;
  }

  .ant-typography-copy,
  .ant-typography-edit {
    width: 18px;
    height: 18px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .ant-typography {
    font-size: 12px;
  }

  .ant-typography-code,
  .ant-typography-keyboard {
    font-size: 80%;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .ant-typography {
    color: rgba(255, 255, 255, 0.85);
  }

  .ant-typography-secondary {
    color: rgba(255, 255, 255, 0.45);
  }

  .ant-typography-disabled {
    color: rgba(255, 255, 255, 0.25);
  }

  .ant-typography-code {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
  }

  .ant-typography-keyboard {
    background: rgba(255, 255, 255, 0.06);
    border-color: rgba(255, 255, 255, 0.2);
    border-bottom-color: rgba(255, 255, 255, 0.25);
    box-shadow: inset 0 -1px 0 rgba(255, 255, 255, 0.25);
  }

  .ant-typography-mark {
    background-color: #d4b106;
    color: #000;
  }

  .ant-typography-delete {
    text-decoration-color: rgba(255, 255, 255, 0.85);
  }

  .ant-typography-underline {
    text-decoration-color: rgba(255, 255, 255, 0.85);
  }

  .ant-typography-copy,
  .ant-typography-edit {
    color: rgba(255, 255, 255, 0.45);
  }

  .ant-typography-copy:hover,
  .ant-typography-edit:hover {
    color: rgba(255, 255, 255, 0.85);
  }

  .ant-typography-edit-content textarea {
    background: #141414;
    border-color: #434343;
    color: rgba(255, 255, 255, 0.85);
  }

  .ant-typography-edit-content textarea:focus {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .ant-typography {
    color: #000;
  }

  .ant-typography-secondary {
    color: #666;
  }

  .ant-typography-disabled {
    color: #999;
  }

  .ant-typography-code,
  .ant-typography-keyboard {
    border-color: #000;
    background: #f0f0f0;
  }

  .ant-typography-copy,
  .ant-typography-edit,
  .ant-typography-expand {
    color: #0066cc;
  }

  .ant-typography-copy:hover,
  .ant-typography-edit:hover,
  .ant-typography-expand:hover {
    color: #004499;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .ant-typography-copy,
  .ant-typography-edit,
  .ant-typography-expand,
  .ant-typography-edit-content textarea {
    transition: none;
  }
}
</style>
