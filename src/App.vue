<template>
  <div id="app">
    <div class="demo-container">
      <h1>Typography.Text Component Demo</h1>
      
      <!-- 基础样式示例 -->
      <section class="demo-section">
        <h2>基础样式</h2>
        <div class="demo-item">
          <label>默认文本:</label>
          <TypographyText>Ant Design (default)</TypographyText>
        </div>
        
        <div class="demo-item">
          <label>代码样式:</label>
          <TypographyText code>Ant Design (code)</TypographyText>
        </div>
        
        <div class="demo-item">
          <label>标记样式:</label>
          <TypographyText mark>Ant Design (mark)</TypographyText>
        </div>
        
        <div class="demo-item">
          <label>删除线:</label>
          <TypographyText delete>Ant Design (delete)</TypographyText>
        </div>
        
        <div class="demo-item">
          <label>下划线:</label>
          <TypographyText underline>Ant Design (underline)</TypographyText>
        </div>
        
        <div class="demo-item">
          <label>粗体:</label>
          <TypographyText strong>Ant Design (strong)</TypographyText>
        </div>
        
        <div class="demo-item">
          <label>斜体:</label>
          <TypographyText italic>Ant Design (italic)</TypographyText>
        </div>
        
        <div class="demo-item">
          <label>键盘样式:</label>
          <TypographyText keyboard>Ant Design (keyboard)</TypographyText>
        </div>
      </section>
      
      <!-- 文本类型示例 -->
      <section class="demo-section">
        <h2>文本类型</h2>
        <div class="demo-item">
          <label>次要文本:</label>
          <TypographyText type="secondary">Ant Design (secondary)</TypographyText>
        </div>
        
        <div class="demo-item">
          <label>成功文本:</label>
          <TypographyText type="success">Ant Design (success)</TypographyText>
        </div>
        
        <div class="demo-item">
          <label>警告文本:</label>
          <TypographyText type="warning">Ant Design (warning)</TypographyText>
        </div>
        
        <div class="demo-item">
          <label>危险文本:</label>
          <TypographyText type="danger">Ant Design (danger)</TypographyText>
        </div>
        
        <div class="demo-item">
          <label>禁用文本:</label>
          <TypographyText disabled>Ant Design (disabled)</TypographyText>
        </div>
      </section>
      
      <!-- 复制功能示例 -->
      <section class="demo-section">
        <h2>复制功能</h2>
        <div class="demo-item">
          <label>基础复制:</label>
          <TypographyText copyable>This is a copyable text.</TypographyText>
        </div>
        
        <div class="demo-item">
          <label>自定义复制文本:</label>
          <TypographyText :copyable="{ text: 'Hello World!' }">Replace copy text.</TypographyText>
        </div>
        
        <div class="demo-item">
          <label>自定义图标和提示:</label>
          <TypographyText :copyable="customCopyConfig">Custom Copy icon and replace tooltips text.</TypographyText>
        </div>
        
        <div class="demo-item">
          <label>隐藏提示:</label>
          <TypographyText :copyable="{ tooltips: false }">Hide Copy tooltips.</TypographyText>
        </div>
      </section>
      
      <!-- 编辑功能示例 -->
      <section class="demo-section">
        <h2>编辑功能</h2>
        <div class="demo-item">
          <label>基础编辑:</label>
          <TypographyText editable @edit-end="handleEditEnd">This is an editable text.</TypographyText>
        </div>
        
        <div class="demo-item">
          <label>限制长度:</label>
          <TypographyText :editable="{ maxLength: 50 }" @edit-change="handleEditChange">
            This is an editable text with limited length.
          </TypographyText>
        </div>
        
        <div class="demo-item">
          <label>自定义图标:</label>
          <TypographyText :editable="customEditConfig">Custom Edit icon and replace tooltip text.</TypographyText>
        </div>
        
        <div class="demo-item">
          <label>文本触发编辑:</label>
          <TypographyText :editable="{ triggerType: ['text'] }">
            Click text to start editing.
          </TypographyText>
        </div>
        
        <div class="demo-item">
          <label>图标和文本都可触发:</label>
          <TypographyText :editable="{ triggerType: ['icon', 'text'] }">
            Text or icon as trigger - click to start editing.
          </TypographyText>
        </div>
      </section>
      
      <!-- 省略功能示例 -->
      <section class="demo-section">
        <h2>省略功能</h2>
        <div class="demo-item">
          <label>单行省略:</label>
          <div style="width: 100%;">
            <TypographyText :ellipsis="{ tooltip: '测试' }">
              Ant Design, a design language for background applications, is refined by Ant UED Team.              Ant Design, a design language for background applications, is refined by Ant UED Team.
              Ant Design, a design language for background applications, is refined by Ant UED Team.
              Ant Design, a design language for background applications, is refined by Ant UED Team.

            </TypographyText>
          </div>
        </div>


        <div class="demo-item">
          <label>可展开的省略:</label>
          <div style="width: 200px;">
            <TypographyText :ellipsis="{ expandable: true }">
              Ant Design, a design language for background applications, is refined by Ant UED Team. 
              Ant Design, a design language for background applications, is refined by Ant UED Team.
            </TypographyText>
          </div>
        </div>
        
        <div class="demo-item">
          <label>多行省略:</label>
          <div>
            <TypographyText :ellipsis="{ rows: 2, expandable: true }">
              Ant Design, a design language for background applications, is refined by Ant UED Team. 
              Ant Design, a design language for background applications, is refined by Ant UED Team. 
              Ant Design, a design language for background applications, is refined by Ant UED Team. 
              Ant Design, a design language for background applications, is refined by Ant UED Team.
            </TypographyText>
          </div>
        </div>
        <div class="demo-item">
          <label>单行省略测试:</label>
          <div style="width:  auto">
            <TypographyText :ellipsis="{ rows: 1, expandable: true }">
              Ant Design, a design language for background applications, is refined by Ant UED Team. 
              Ant Design, a design language for background applications, is refined by Ant UED Team. 
              Ant Design, a design language for background applications, is refined by Ant UED Team. 
              Ant Design, a design language for background applications, is refined by Ant UED Team.
            </TypographyText>
          </div>
        </div>
        
        <div class="demo-item">
          <label>自定义后缀:</label>
          <div style="width: 200px;">
            <TypographyText :ellipsis="{ suffix: '--William Shakespeare', expandable: true }">
              To be, or not to be, that is the question: Whether it is nobler in the mind to suffer.
            </TypographyText>
          </div>
        </div>
      </section>
      
      <!-- 组合功能示例 -->
      <section class="demo-section">
        <h2>组合功能</h2>
        <div class="demo-item">
          <label>复制 + 编辑:</label>
          <TypographyText copyable editable strong type="success">
            This text supports both copy and edit.
          </TypographyText>
        </div>
        
        <div class="demo-item">
          <label>代码 + 复制:</label>
          <TypographyText code copyable>
            npm install antd
          </TypographyText>
        </div>
        
        <div class="demo-item">
          <label>标记 + 删除线:</label>
          <TypographyText mark delete>
            This text is marked and deleted.
          </TypographyText>
        </div>
      </section>
      
      <!-- 事件处理示例 -->
      <section class="demo-section">
        <h2>事件处理</h2>
        <div class="demo-item">
          <label>点击事件:</label>
          <TypographyText @click="handleClick" style="cursor: pointer; color: #1890ff;">
            Click me to trigger event
          </TypographyText>
        </div>
        
        <div class="demo-item">
          <label>复制事件:</label>
          <TypographyText copyable @copy="handleCopy">
            Copy me and check console
          </TypographyText>
        </div>
        
        <div class="demo-item">
          <label>编辑事件:</label>
          <TypographyText 
            editable 
            @edit-start="handleEditStart"
            @edit-end="handleEditEnd"
            @edit-cancel="handleEditCancel"
          >
            Edit me and check console
          </TypographyText>
        </div>
      </section>
      
      <!-- 消息显示 -->
      <div v-if="message" class="message">
        {{ message }}
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import TypographyText from './components/Typography/Text.vue'
import { Tooltip } from 'ant-design-vue'

export default {
  name: 'App',
  components: {
    TypographyText,
    'a-tooltip': Tooltip
  },
  setup() {
    const message = ref('')
    
    // 自定义复制配置
    const customCopyConfig = {
      icon: ['📋', '✅'],
      tooltips: ['点击复制', '复制成功'],
      onCopy: (event, { text }) => {
        console.log('Custom copy callback:', text)
      }
    }
    
    // 自定义编辑配置
    const customEditConfig = {
      icon: '🖊️',
      tooltip: '点击编辑',
      enterIcon: '✅'
    }
    
    // 事件处理函数
    const handleClick = (event) => {
      message.value = 'Text clicked!'
      setTimeout(() => message.value = '', 2000)
      console.log('Text clicked:', event)
    }
    
    const handleCopy = ({ text }) => {
      message.value = `Copied: ${text}`
      setTimeout(() => message.value = '', 2000)
      console.log('Text copied:', text)
    }
    
    const handleEditStart = () => {
      message.value = 'Edit started'
      setTimeout(() => message.value = '', 2000)
      console.log('Edit started')
    }
    
    const handleEditEnd = (text) => {
      message.value = `Edit ended: ${text}`
      setTimeout(() => message.value = '', 2000)
      console.log('Edit ended:', text)
    }
    
    const handleEditCancel = () => {
      message.value = 'Edit cancelled'
      setTimeout(() => message.value = '', 2000)
      console.log('Edit cancelled')
    }
    
    const handleEditChange = (text) => {
      console.log('Edit changed:', text)
    }
    
    return {
      message,
      customCopyConfig,
      customEditConfig,
      handleClick,
      handleCopy,
      handleEditStart,
      handleEditEnd,
      handleEditCancel,
      handleEditChange
    }
  }
}
</script>

<style>
#app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-container {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.demo-section {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.demo-section:last-child {
  border-bottom: none;
}

.demo-section h2 {
  color: #262626;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 16px;
  border-left: 4px solid #1890ff;
  padding-left: 12px;
}

.demo-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 0;
}

.demo-item label {
  min-width: 150px;
  font-weight: 500;
  color: #595959;
  margin-right: 16px;
}

.message {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #52c41a;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

/* 响应式设计 */
@media (max-width: 768px) {
  #app {
    padding: 10px;
  }
  
  .demo-container {
    padding: 16px;
  }
  
  .demo-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .demo-item label {
    min-width: auto;
    margin-bottom: 4px;
    margin-right: 0;
  }
}
</style>
