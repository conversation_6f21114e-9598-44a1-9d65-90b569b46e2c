<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Typography Text Component Test</title>
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      padding: 20px;
      line-height: 1.6;
    }
    .test-section {
      margin-bottom: 30px;
      padding: 20px;
      border: 1px solid #e8e8e8;
      border-radius: 8px;
    }
    .test-item {
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    .test-label {
      min-width: 120px;
      font-weight: bold;
      color: #666;
    }
    h2 {
      color: #1890ff;
      border-bottom: 2px solid #1890ff;
      padding-bottom: 5px;
    }
  </style>
</head>
<body>
  <div id="app">
    <h1>Typography.Text Component 功能测试</h1>
    
    <div class="test-section">
      <h2>基础样式测试</h2>
      <div class="test-item">
        <span class="test-label">默认文本:</span>
        <typography-text>Ant Design (default)</typography-text>
      </div>
      <div class="test-item">
        <span class="test-label">代码样式:</span>
        <typography-text code>Ant Design (code)</typography-text>
      </div>
      <div class="test-item">
        <span class="test-label">标记样式:</span>
        <typography-text mark>Ant Design (mark)</typography-text>
      </div>
      <div class="test-item">
        <span class="test-label">删除线:</span>
        <typography-text delete>Ant Design (delete)</typography-text>
      </div>
      <div class="test-item">
        <span class="test-label">下划线:</span>
        <typography-text underline>Ant Design (underline)</typography-text>
      </div>
      <div class="test-item">
        <span class="test-label">粗体:</span>
        <typography-text strong>Ant Design (strong)</typography-text>
      </div>
      <div class="test-item">
        <span class="test-label">斜体:</span>
        <typography-text italic>Ant Design (italic)</typography-text>
      </div>
      <div class="test-item">
        <span class="test-label">键盘样式:</span>
        <typography-text keyboard>Ant Design (keyboard)</typography-text>
      </div>
    </div>
    
    <div class="test-section">
      <h2>文本类型测试</h2>
      <div class="test-item">
        <span class="test-label">次要文本:</span>
        <typography-text type="secondary">Ant Design (secondary)</typography-text>
      </div>
      <div class="test-item">
        <span class="test-label">成功文本:</span>
        <typography-text type="success">Ant Design (success)</typography-text>
      </div>
      <div class="test-item">
        <span class="test-label">警告文本:</span>
        <typography-text type="warning">Ant Design (warning)</typography-text>
      </div>
      <div class="test-item">
        <span class="test-label">危险文本:</span>
        <typography-text type="danger">Ant Design (danger)</typography-text>
      </div>
      <div class="test-item">
        <span class="test-label">禁用文本:</span>
        <typography-text disabled>Ant Design (disabled)</typography-text>
      </div>
    </div>
    
    <div class="test-section">
      <h2>交互功能测试</h2>
      <div class="test-item">
        <span class="test-label">复制功能:</span>
        <typography-text copyable>This is a copyable text.</typography-text>
      </div>
      <div class="test-item">
        <span class="test-label">编辑功能:</span>
        <typography-text editable>This is an editable text.</typography-text>
      </div>
      <div class="test-item">
        <span class="test-label">省略功能:</span>
        <div style="width: 200px;">
          <typography-text :ellipsis="{ expandable: true }">
            Ant Design, a design language for background applications, is refined by Ant UED Team.
          </typography-text>
        </div>
      </div>
    </div>
    
    <div class="test-section">
      <h2>组合功能测试</h2>
      <div class="test-item">
        <span class="test-label">复制+编辑:</span>
        <typography-text copyable editable strong type="success">
          This text supports both copy and edit.
        </typography-text>
      </div>
      <div class="test-item">
        <span class="test-label">代码+复制:</span>
        <typography-text code copyable>npm install antd</typography-text>
      </div>
    </div>
    
    <div v-if="message" style="position: fixed; top: 20px; right: 20px; background: #52c41a; color: white; padding: 10px; border-radius: 4px;">
      {{ message }}
    </div>
  </div>

  <script>
    const { createApp, ref, computed, nextTick, onMounted, watch } = Vue;
    
    // Typography Text Component (简化版本用于测试)
    const TypographyText = {
      name: 'TypographyText',
      props: {
        code: { type: Boolean, default: false },
        copyable: { type: [Boolean, Object], default: false },
        delete: { type: Boolean, default: false },
        disabled: { type: Boolean, default: false },
        editable: { type: [Boolean, Object], default: false },
        ellipsis: { type: [Boolean, Object], default: false },
        keyboard: { type: Boolean, default: false },
        mark: { type: Boolean, default: false },
        strong: { type: Boolean, default: false },
        italic: { type: Boolean, default: false },
        type: { type: String },
        underline: { type: Boolean, default: false },
        text: { type: String, default: '' }
      },
      template: `
        <component
          :is="tag"
          :class="textClasses"
          @click="handleClick"
        >
          <span v-if="!isEditing">
            <slot>{{ displayText }}</slot>
          </span>
          <textarea
            v-else
            v-model="editValue"
            @blur="handleEditEnd"
            @keydown.enter="handleEditEnd"
            @keydown.esc="handleEditCancel"
            style="border: 1px solid #d9d9d9; padding: 4px; border-radius: 4px; font-family: inherit;"
          />
          <button v-if="copyableConfig" @click="handleCopy" style="margin-left: 8px; background: none; border: none; cursor: pointer;">
            📋
          </button>
          <button v-if="editableConfig && !isEditing" @click="handleEditStart" style="margin-left: 8px; background: none; border: none; cursor: pointer;">
            ✏️
          </button>
          <button v-if="ellipsisConfig && canExpand" @click="handleExpand" style="margin-left: 8px; background: none; border: none; cursor: pointer; color: #1890ff;">
            {{ isExpanded ? 'Collapse' : 'Expand' }}
          </button>
        </component>
      `,
      setup(props, { slots, emit }) {
        const isEditing = ref(false);
        const editValue = ref('');
        const isExpanded = ref(false);
        const displayText = ref('');
        const canExpand = ref(false);
        
        const tag = computed(() => {
          if (props.code) return 'code';
          if (props.keyboard) return 'kbd';
          if (props.mark) return 'mark';
          return 'span';
        });
        
        const textClasses = computed(() => {
          const classes = ['ant-typography'];
          if (props.code) classes.push('ant-typography-code');
          if (props.delete) classes.push('ant-typography-delete');
          if (props.disabled) classes.push('ant-typography-disabled');
          if (props.keyboard) classes.push('ant-typography-keyboard');
          if (props.mark) classes.push('ant-typography-mark');
          if (props.strong) classes.push('ant-typography-strong');
          if (props.italic) classes.push('ant-typography-italic');
          if (props.underline) classes.push('ant-typography-underline');
          if (props.type) classes.push(`ant-typography-${props.type}`);
          return classes;
        });
        
        const copyableConfig = computed(() => props.copyable ? {} : null);
        const editableConfig = computed(() => props.editable ? {} : null);
        const ellipsisConfig = computed(() => props.ellipsis ? {} : null);
        
        const handleClick = () => {
          if (editableConfig.value && !isEditing.value) {
            handleEditStart();
          }
        };
        
        const handleCopy = async () => {
          try {
            await navigator.clipboard.writeText(displayText.value);
            emit('message', 'Copied!');
          } catch (error) {
            console.error('Copy failed:', error);
          }
        };
        
        const handleEditStart = () => {
          editValue.value = displayText.value;
          isEditing.value = true;
        };
        
        const handleEditEnd = () => {
          displayText.value = editValue.value;
          isEditing.value = false;
          emit('message', 'Edit completed!');
        };
        
        const handleEditCancel = () => {
          isEditing.value = false;
          emit('message', 'Edit cancelled!');
        };
        
        const handleExpand = () => {
          isExpanded.value = !isExpanded.value;
          emit('message', isExpanded.value ? 'Expanded!' : 'Collapsed!');
        };
        
        onMounted(() => {
          displayText.value = props.text || (slots.default?.()[0]?.children || '');
          if (ellipsisConfig.value) {
            canExpand.value = true;
          }
        });
        
        return {
          tag,
          textClasses,
          copyableConfig,
          editableConfig,
          ellipsisConfig,
          isEditing,
          editValue,
          isExpanded,
          displayText,
          canExpand,
          handleClick,
          handleCopy,
          handleEditStart,
          handleEditEnd,
          handleEditCancel,
          handleExpand
        };
      }
    };
    
    // 主应用
    createApp({
      components: {
        TypographyText
      },
      setup() {
        const message = ref('');
        
        return {
          message
        };
      }
    }).mount('#app');
  </script>
  
  <!-- 组件样式 -->
  <style>
    .ant-typography {
      color: rgba(0, 0, 0, 0.85);
      font-size: 14px;
      line-height: 1.5715;
      margin: 0;
      padding: 0;
      display: inline;
    }
    
    .ant-typography-code {
      margin: 0 0.2em;
      padding: 0.2em 0.4em 0.1em;
      font-size: 85%;
      background: rgba(150, 150, 150, 0.1);
      border: 1px solid rgba(100, 100, 100, 0.2);
      border-radius: 3px;
      font-family: 'SFMono-Regular', Consolas, monospace;
    }
    
    .ant-typography-mark {
      background-color: #ffe58f;
      padding: 0;
    }
    
    .ant-typography-delete {
      text-decoration: line-through;
    }
    
    .ant-typography-underline {
      text-decoration: underline;
    }
    
    .ant-typography-strong {
      font-weight: 600;
    }
    
    .ant-typography-italic {
      font-style: italic;
    }
    
    .ant-typography-keyboard {
      margin: 0 0.2em;
      padding: 0.15em 0.4em 0.1em;
      font-size: 90%;
      background: rgba(150, 150, 150, 0.06);
      border: 1px solid rgba(100, 100, 100, 0.2);
      border-radius: 3px;
      font-family: 'SFMono-Regular', Consolas, monospace;
    }
    
    .ant-typography-disabled {
      color: rgba(0, 0, 0, 0.25);
      cursor: not-allowed;
    }
    
    .ant-typography-secondary {
      color: rgba(0, 0, 0, 0.45);
    }
    
    .ant-typography-success {
      color: #52c41a;
    }
    
    .ant-typography-warning {
      color: #faad14;
    }
    
    .ant-typography-danger {
      color: #ff4d4f;
    }
  </style>
</body>
</html>
