{"name": "typography-text-component", "version": "1.0.0", "type": "module", "description": "A complete implementation of Ant Design Vue Typography.Text component", "main": "src/components/Typography/Text.vue", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "echo \"Tests will be implemented in the next task\" && exit 0"}, "keywords": ["vue", "vue3", "antd", "ant-design", "typography", "text", "component"], "author": "Your Name", "license": "MIT", "dependencies": {"@vue/compiler-sfc": "^3.5.17", "ant-design-vue": "^3.2.20", "typescript": "^5.8.3", "vue": "^3.5.17", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.0.0", "vite": "^4.0.0"}}